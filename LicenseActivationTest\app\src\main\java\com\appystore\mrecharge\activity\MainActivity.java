package com.appystore.mrecharge.activity;


import android.Manifest;
import android.accessibilityservice.AccessibilityServiceInfo;
import android.app.ActivityManager;
import android.app.AlertDialog;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.ProgressDialog;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.graphics.Color;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.net.Uri;
import android.os.AsyncTask;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.PowerManager;
import android.preference.PreferenceManager;
import android.provider.Settings;
import android.text.TextUtils;
import android.util.Log;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.accessibility.AccessibilityManager;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.CompoundButton;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.Spinner;
import android.widget.SpinnerAdapter;
import android.widget.TextView;
import android.widget.Toast;
import android.widget.ToggleButton;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.app.NotificationCompat;
import androidx.core.app.NotificationManagerCompat;
import androidx.core.content.ContextCompat;
import androidx.work.OneTimeWorkRequest;
import androidx.work.WorkManager;
import com.android.volley.AuthFailureError;
import com.android.volley.DefaultRetryPolicy;
import com.android.volley.Request;
import com.android.volley.RequestQueue;
import com.android.volley.Response;
import com.android.volley.VolleyError;
import com.android.volley.toolbox.StringRequest;
import com.android.volley.toolbox.Volley;
import com.appystore.mrecharge.NetworkClient;
import com.appystore.mrecharge.R;
import com.appystore.mrecharge.WResponse;
import com.appystore.mrecharge.service.USSDService;
import com.appystore.mrecharge.service.sever;
import com.appystore.mrecharge.util.Devicer;
import com.google.android.gms.common.internal.ImagesContract;
import com.google.firebase.FirebaseApp;
// Firebase imports removed to prevent crashes
import java.io.OutputStream;
import java.util.ArrayList;
import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Hashtable;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.TimeZone;
import java.util.UUID;
import javax.security.auth.Subject;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import retrofit2.Call;
import retrofit2.Callback;


public class MainActivity extends AppCompatActivity {
    private static final int IGNORE_BATTERY_OPTIMIZATION_REQUEST = 1002;
    private static final int MY_PERMISSIONS_REQUEST_READ_PHONE_STATE = 0;
    private static final int PERMISSION_REQUEST_CODE = 1;
    private static final int MAX_BUTTON_CLICKS = 7;
    private static final int BUTTON_COOLDOWN_TIME = 5 * 60 * 1000; // 5 minutes in milliseconds
    private String ACCESSIBILITY_SERVICE_NAME;
    String FinalJSonObject;
    boolean al;
    String alert;
    BroadcastReceiver br;
    ToggleButton buttonClick;
    String emid;
    boolean flag;
    private Handler handler;
    boolean isReceiverRegistered;
    private BroadcastReceiver mRegistrationBroadcastReceiver;
    OneTimeWorkRequest mRequest;
    WorkManager mWorkManager;
    EditText mpin;
    TextView myidt;
    private ProgressDialog pDialog;
    private RadioButton radioButton;
    private RadioGroup radioGroup;
    private RadioGroup radioGroup2;

    // Variables for tracking button clicks
    private int buttonClickCount = 0;
    private long lastClickResetTime = 0;
    private boolean buttonCooldownActive = false;
    private RadioGroup radioGroup3;
    private RequestQueue requestQueue;
    private Runnable runnable;
    String servname;
    String sf;
    String sim1;
    String sim2;
    Spinner simn1;
    Spinner simn2;
    SharedPreferences sp;
    EditText url;
    String xemid;
    private static final String[] distic = {"OFF", "Grameenphone", "Robi", "Banglalink", "Airtel", "Teletalk"};
    private static final String TAG = MainActivity.class.getSimpleName();
    public static int ACTION_MANAGE_OVERLAY_PERMISSION_REQUEST_CODE = 2323;
    String[] permissions = {"android.permission.SEND_SMS", "android.permission.CALL_PHONE"};
    private final int runTime = 5000;
    private int MY_PERMISSIONS_REQUEST_SMS_RECEIVE = 10;
    int PERMISSION_ALL = 1;
    String[] PERMISSIONS = {"android.permission.SEND_SMS", "android.permission.CALL_PHONE", "android.permission.RECEIVE_SMS", "android.permission.READ_SMS", "android.permission.READ_PHONE_STATE", "android.permission.RECEIVE_BOOT_COMPLETED", "android.permission.WAKE_LOCK"};


    //------------------------------- for license Part-------------------------------------------//

    // UI Components
    private EditText licenseKeyField;
    private EditText pinField;
    private Button activateButton;
    private TextView statusText;
    private TextView expirationText;
    private TextView countdownText;
    private ProgressBar expirationProgressBar;
    private View expirationSection;

    // Domain login UI components
    private View domainLoginSection;
    private TextView domainInfoText;
    private Button domainLoginButton;

    // Layout containers for visibility management
    private View licenseActivationLayout;
    private View btnSpeakContainer;
    private View mainAppContent;

    // Authentication state
    private boolean isActivated = false;
    private String pendingApprovalLogId = null;
    private Runnable approvalCheckRunnable;

    // Switch/Toggle functionality state
    private boolean isApprovalPending = false;
    private boolean switchAutoEnableOnApproval = false;

    // Expiration management
    private long licenseExpirationTime = 0;
    private Runnable expirationCheckRunnable;
    private Runnable warningNotificationRunnable;
    private boolean isInWarningPeriod = false;
    private boolean isExpired = false;
    private long lastWarningTime = 0;

    // Constants
    private static final String DEFAULT_API_URL = "http://192.168.0.106/AppyStoreMRecharge/admin/api/device_auth.php";
    //private static final String DEFAULT_API_URL = "https://telelicense.diderappstore.top/admin/api/device_auth.php";
    private static final int APPROVAL_CHECK_INTERVAL = 10000; // 10 seconds
    private static final int MAX_APPROVAL_CHECKS = 30; // 5 minutes total
    private int approvalCheckCount = 0;

    // Block status checking
    private static final int BLOCK_CHECK_INTERVAL = 30000; // 30 seconds
    private Runnable blockStatusRunnable;
    
    // License status check methods
    private boolean isLicenseBlocked() {
        // First check local preference
        SharedPreferences prefs = getSharedPreferences("LicensePrefs", MODE_PRIVATE);
        boolean isBlocked = prefs.getBoolean("is_blocked", false);
        
        // If we have a server check result, use that instead
        if (prefs.contains("last_server_check_blocked")) {
            isBlocked = prefs.getBoolean("last_server_check_blocked", false);
        }
        
        // If not blocked locally, we can do a quick server check
        if (!isBlocked) {
            checkLicenseWithServer();
        }
        
        return isBlocked;
    }
    
    private boolean isLicenseExpired() {
        // Get expiration date from SharedPreferences
        SharedPreferences prefs = getSharedPreferences("LicensePrefs", MODE_PRIVATE);
        long expirationTime = prefs.getLong("expiration_time", 0);
        
        // If we have a server check result, use that instead
        if (prefs.contains("last_server_check_expiration")) {
            expirationTime = prefs.getLong("last_server_check_expiration", 0);
        } else if (expirationTime == 0) {
            // If no expiration time is set, check with server
            checkLicenseWithServer();
            return false; // Assume not expired until we hear back from server
        }
        
        // Check if current time is after expiration time
        boolean expired = expirationTime > 0 && System.currentTimeMillis() > expirationTime;
        
        // If expired, do one more check with server to be sure
        if (expired) {
            checkLicenseWithServer();
        }
        
        return expired;
    }
    
    private void checkLicenseWithServer() {
        // This method would make an API call to your server to verify the license status
        Log.d("LicenseCheck", "Checking license status with server...");
        
        // Get device ID for the API call
        String deviceId = getPrefid("myid", getApplicationContext());
        if (deviceId == null || deviceId.isEmpty()) {
            Log.e("LicenseCheck", "Device ID not available");
            return;
        }
        
        // Example API call using Volley (you'll need to implement your actual API endpoint)
        String url = getApiUrl() + "?action=check_license&device_id=" + deviceId;
        
        StringRequest stringRequest = new StringRequest(Request.Method.GET, url,
            new Response.Listener<String>() {
                @Override
                public void onResponse(String response) {
                    try {
                        JSONObject jsonResponse = new JSONObject(response);
                        boolean success = jsonResponse.getBoolean("success");
                        
                        if (success) {
                            JSONObject data = jsonResponse.getJSONObject("data");
                            boolean isBlocked = data.optBoolean("is_blocked", false);
                            long expirationTime = data.optLong("expiration_time", 0);
                            
                            // Update local preferences with server response
                            SharedPreferences.Editor editor = getSharedPreferences("LicensePrefs", MODE_PRIVATE).edit();
                            editor.putBoolean("last_server_check_blocked", isBlocked);
                            editor.putLong("last_server_check_expiration", expirationTime);
                            editor.apply();
                            
                            // Update UI if needed
                            runOnUiThread(new Runnable() {
                                @Override
                                public void run() {
                                    updateToggleButtonState();
                                    
                                    // If license is now invalid, show activation UI
                                    if (isBlocked || (expirationTime > 0 && System.currentTimeMillis() > expirationTime)) {
                                        checkLicenseAndShowUI();
                                    }
                                }
                            });
                        }
                    } catch (JSONException e) {
                        Log.e("LicenseCheck", "Error parsing server response", e);
                    }
                }
            },
            new Response.ErrorListener() {
                @Override
                public void onErrorResponse(VolleyError error) {
                    Log.e("LicenseCheck", "Error checking license status: " + error.getMessage());
                }
            });
        
        // Add the request to the RequestQueue
        if (requestQueue == null) {
            requestQueue = Volley.newRequestQueue(this);
        }
        requestQueue.add(stringRequest);
    }
    
    private void updateToggleButtonState() {
        if (buttonClick == null) {
            return;
        }
        
        boolean isBlocked = isLicenseBlocked();
        boolean isExpired = isLicenseExpired();
        
        // Disable the button if license is invalid
        buttonClick.setEnabled(!(isBlocked || isExpired));
        
        // If license is invalid, ensure the button is toggled off
        if (isBlocked || isExpired) {
            buttonClick.setChecked(false);
        }
    }
    
    private void checkLicenseAndShowUI() {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (isLicenseBlocked() || isLicenseExpired()) {
                    try {
                        // Show license activation UI and hide main content
                        View licenseActivation = findViewById(R.id.license_activation);
                        View mainAppContent = findViewById(R.id.mainAppContent);
                        
                        if (licenseActivation != null) {
                            // Show license activation UI
                            licenseActivation.setVisibility(View.VISIBLE);
                            
                            // Hide main app content if it exists
                            if (mainAppContent != null) {
                                mainAppContent.setVisibility(View.GONE);
                            }
                            
                            // Show appropriate message
                            String message = isLicenseBlocked() ? 
                                "Your license has been blocked. Please contact support." : 
                                "Your license has expired. Please renew your subscription.";
                            Toast.makeText(MainActivity.this, message, Toast.LENGTH_LONG).show();
                            
                            // Scroll to the license activation section
                            licenseActivation.post(new Runnable() {
                                @Override
                                public void run() {
                                    licenseActivation.requestFocus();
                                }
                            });
                        }
                    } catch (Exception e) {
                        Log.e("LicenseCheck", "Error showing license activation UI", e);
                    }
                }
            }
        });
    }

    // Notification permission constants
    private static final int NOTIFICATION_PERMISSION_REQUEST_CODE = 1001;
    private static final String NOTIFICATION_PERMISSION_REQUESTED_KEY = "notification_permission_requested";

    // Expiration constants
    private static final long WARNING_PERIOD_MS = 48 * 60 * 60 * 1000L; // 48 hours
    private static final long FINAL_WARNING_PERIOD_MS = 24 * 60 * 60 * 1000L; // 24 hours
    private static final long WARNING_NOTIFICATION_INTERVAL = 5 * 60 * 1000L; // 5 minutes
    private static final long EXPIRATION_CHECK_INTERVAL = 1000L; // 1 second for real-time countdown
    private static final String NOTIFICATION_CHANNEL_ID = "license_expiration";
    private static final int NOTIFICATION_ID_WARNING = 1001;
    private static final int NOTIFICATION_ID_EXPIRED = 1002;
    //------------------------------- for license Part-------------------------------------------//
    @Override
    protected void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        setContentView(R.layout.activity_main);
        // Initialize handler
        handler = new Handler();

        // Initialize notification channel
        createNotificationChannel();

        // Check and request notification permissions
        checkNotificationPermissions();

        // Initialize UI components
        initializeUI();

        // Check if already activated
        checkActivationStatus();

        // Initialize expiration management
        initializeExpirationManagement();

        Log.d(TAG, "MainActivity created");
        // Initialize handler for button cooldown
        //handler = new Handler();

        // Initialize click tracking
        resetButtonClickCount();

        // Initialize Firebase if not already initialized
        try {
            FirebaseApp.initializeApp(this);
            Log.d("FIREBASE", "Firebase initialized successfully");
        } catch (Exception e) {
            Log.e("FIREBASE", "Error initializing Firebase: " + e.getMessage(), e);
        }

        // Log the device ID for debugging
        String deviceId = getPrefid("myid", getApplicationContext());
        Log.d("DEVICE_INFO", "Device ID: " + deviceId);
        Log.d("DEVICE_INFO", "Device Name: " + device_name());

        // Check for license key mismatch on startup
        checkLicenseKeyMismatch();
        this.simn1 = (Spinner) findViewById(R.id.simaname);
        this.simn2 = (Spinner) findViewById(R.id.simbname);
        ActivityCompat.requestPermissions(this, this.PERMISSIONS, 1);
        this.sp = getSharedPreferences("pref", 0);
        Log.e("1003", this.sp.getString("token", ""));
        RadioButton radioButton = (RadioButton) findViewById(R.id.newm);
        ArrayAdapter arrayAdapter = new ArrayAdapter(this, android.R.layout.simple_spinner_item, distic);
        arrayAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        this.simn1.setAdapter((SpinnerAdapter) arrayAdapter);
        ArrayAdapter arrayAdapter2 = new ArrayAdapter(this, android.R.layout.simple_spinner_item, distic);
        arrayAdapter2.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        this.simn2.setAdapter((SpinnerAdapter) arrayAdapter2);
        this.radioGroup = (RadioGroup) findViewById(R.id.server_type);
        if (this.sp.getBoolean("gateway_on", false)) {
            radioButton.setChecked(true);
        } else {
            radioButton.setChecked(false);
        }
        // Safely find and cast views
        View bankView = findViewById(R.id.bank);
        View smsView = findViewById(R.id.sms);
        View monView = findViewById(R.id.mon);

        LinearLayout linearLayout = null;
        LinearLayout linearLayout2 = null;
        LinearLayout linearLayout3 = null;

        // Only cast to LinearLayout if the views are actually LinearLayouts
        if (bankView instanceof LinearLayout) {
            linearLayout = (LinearLayout) bankView;
        }

        if (smsView instanceof LinearLayout) {
            linearLayout2 = (LinearLayout) smsView;
        }

        if (monView instanceof LinearLayout) {
            linearLayout3 = (LinearLayout) monView;
        }
        ToggleButton toggleButton = (ToggleButton) findViewById(R.id.auto);
        final ToggleButton toggleButton2 = (ToggleButton) findViewById(R.id.bettery);
        this.buttonClick = (ToggleButton) findViewById(R.id.sav);
        // Add click listener to check license status when ToggleButton is clicked
        this.buttonClick.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // Check license status before allowing the toggle
                if (isLicenseBlocked() || isLicenseExpired()) {
                    // Prevent the toggle if license is blocked or expired
                    buttonClick.setChecked(!buttonClick.isChecked()); // Revert the toggle
                    checkLicenseAndShowUI(); // Show license activation UI
                }
                // If license is valid, allow the toggle to proceed
            }
        });
        
        ToggleButton toggleButton3 = (ToggleButton) findViewById(R.id.sms_service);
        getWindow().setSoftInputMode(2);
        this.url = (EditText) findViewById(R.id.url);
        this.mpin = (EditText) findViewById(R.id.dpin);
        this.myidt = (TextView) findViewById(R.id.myid);
        if (TextUtils.isEmpty(getPrefid("myid", getApplicationContext()))) {
            SavePreferences("myid", randomCode());
        }
        this.myidt.setText("Device ID: " + getPrefid("myid", getApplicationContext()));
        String pref = getPref("licence", getApplicationContext());
        if (!TextUtils.isEmpty(pref)) {
            this.url.setText(pref);
        }
        ToggleButton toggleButton4 = (ToggleButton) findViewById(R.id.next);
        this.mpin.setText(getPrefx("pin", getApplicationContext()));
        SharedPreferences sharedPreferences = getSharedPreferences("serv", 0);
        final int i = sharedPreferences.getInt("sima_service", 0);
        sharedPreferences.getInt("simb_service", 0);
        this.simn1.setSelection(sharedPreferences.getInt("simap", 0));
        this.simn2.setSelection(sharedPreferences.getInt("simbp", 0));
        int i2 = sharedPreferences.getInt("stop", 0);
        if (sharedPreferences.getInt("page", 0) == 1) {
            toggleButton4.setChecked(true);
        } else {
            toggleButton4.setChecked(false);
        }
        if (i2 == 1 && isMyServiceRunning(sever.class)) {
            this.buttonClick.setChecked(true);
        } else {
            this.buttonClick.setChecked(false);
        }
        if (sharedPreferences.getInt("sms_service", 0) == 1) {
            toggleButton3.setChecked(true);
        } else {
            toggleButton3.setChecked(false);
        }
        if (isAccessibilitySettingsOn(this)) {
            toggleButton.setChecked(true);
        } else {
            toggleButton.setChecked(false);
        }
        final PowerManager powerManager = (PowerManager) getSystemService(Context.POWER_SERVICE);
        if (Build.VERSION.SDK_INT >= 23) {
            if (powerManager != null && !powerManager.isIgnoringBatteryOptimizations(getPackageName())) {
                askIgnoreOptimization();
                toggleButton2.setChecked(false);
            } else {
                toggleButton2.setChecked(true);
            }
        }
        this.simn1.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override // android.widget.AdapterView.OnItemSelectedListener
            public void onNothingSelected(AdapterView<?> adapterView) {
            }

            @Override // android.widget.AdapterView.OnItemSelectedListener
            public void onItemSelected(AdapterView<?> adapterView, View view, int i3, long j) {
                int i4 = i;
                if (i3 == 0) {
                    MainActivity.this.servname = "NO";
                }
                if (i3 == 1) {
                    MainActivity.this.servname = "GP";
                }
                if (i3 == 2) {
                    MainActivity.this.servname = "RB";
                }
                if (i3 == 3) {
                    MainActivity.this.servname = "BL";
                }
                if (i3 == 4) {
                    MainActivity.this.servname = "AT";
                }
                if (i3 == 5) {
                    MainActivity.this.servname = "TT";
                }
                SharedPreferences.Editor edit = MainActivity.this.getSharedPreferences("serv", 0).edit();
                edit.putInt("simap", i3);
                edit.commit();
                MainActivity mainActivity = MainActivity.this;
                mainActivity.SavePreferences("m1", mainActivity.servname);
            }
        });
        this.simn2.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override // android.widget.AdapterView.OnItemSelectedListener
            public void onNothingSelected(AdapterView<?> adapterView) {
            }

            @Override // android.widget.AdapterView.OnItemSelectedListener
            public void onItemSelected(AdapterView<?> adapterView, View view, int i3, long j) {
                int i4 = i;
                if (i3 == 0) {
                    MainActivity.this.servname = "NO";
                }
                if (i3 == 1) {
                    MainActivity.this.servname = "GP";
                }
                if (i3 == 2) {
                    MainActivity.this.servname = "RB";
                }
                if (i3 == 3) {
                    MainActivity.this.servname = "BL";
                }
                if (i3 == 4) {
                    MainActivity.this.servname = "AT";
                }
                if (i3 == 5) {
                    MainActivity.this.servname = "TT";
                }
                SharedPreferences.Editor edit = MainActivity.this.getSharedPreferences("serv", 0).edit();
                edit.putInt("simbp", i3);
                edit.commit();
                MainActivity mainActivity = MainActivity.this;
                mainActivity.SavePreferences("m2", mainActivity.servname);
            }
        });
        toggleButton.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton compoundButton, boolean z) {
                MainActivity.this.startActivityForResult(new Intent("android.settings.ACCESSIBILITY_SETTINGS"), 0);
            }
        });
        toggleButton2.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton compoundButton, boolean z) {
                if (Build.VERSION.SDK_INT >= 23) {
                    PowerManager powerManager2 = powerManager;
                    if (powerManager2 != null && !powerManager2.isIgnoringBatteryOptimizations(MainActivity.this.getPackageName())) {
                        MainActivity.this.askIgnoreOptimization();
                        toggleButton2.setChecked(true);
                    } else {
                        toggleButton2.setChecked(false);
                    }
                }
            }
        });
        toggleButton3.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton compoundButton, boolean z) {
                int i3;
                if (z) {
                    i3 = 1;
                    MainActivity.this.servname = "SM";
                } else {
                    MainActivity.this.servname = "OFF";
                    i3 = 0;
                }
                SharedPreferences.Editor edit = MainActivity.this.getSharedPreferences("serv", 0).edit();
                edit.putInt("sms_service", i3);
                edit.commit();
                MainActivity mainActivity = MainActivity.this;
                mainActivity.SavePreferences("m3", mainActivity.servname);
            }
        });
        toggleButton4.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton compoundButton, boolean z) {
                SharedPreferences.Editor edit = MainActivity.this.getSharedPreferences("serv", 0).edit();
                edit.putInt("page", z ? 1 : 0);
                edit.commit();
            }
        });
        this.buttonClick.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton compoundButton, boolean z) {
                // Check if button is in cooldown
                if (buttonCooldownActive) {
                    // Don't allow toggling during cooldown
                    compoundButton.setChecked(!z);
                    Toast.makeText(MainActivity.this, "Button is disabled for 5 minutes due to excessive clicks", Toast.LENGTH_LONG).show();
                    return;
                }

                // Check if approval is pending
                if (isApprovalPending && z) {
                    // Show waiting for approval status
                    showApprovalPendingStatus();
                    // Keep the switch in pending state (don't actually enable)
                    compoundButton.setChecked(false);
                    switchAutoEnableOnApproval = true; // Remember to enable when approved
                    savePreference("switch_auto_enable", true);
                    return;
                }

                // Increment click counter
                incrementButtonClickCount();

                // Check if we need to show warning or disable button
                if (buttonClickCount == 3) {
                    // Show warning alert at 3 clicks
                    showWarningAlert();
                } else if (buttonClickCount >= MAX_BUTTON_CLICKS) {
                    // Show danger alert and disable button at 4 clicks
                    showDangerAlertAndDisableButton();
                    compoundButton.setChecked(!z);
                    return;
                }

                // Continue with normal button functionality
                if (MainActivity.this.radioGroup.getCheckedRadioButtonId() == R.id.olmd) {
                    MainActivity.this.getSharedPreferences("pref", 0).edit().putBoolean("gateway_on", false).apply();
                } else {
                    MainActivity.this.getSharedPreferences("pref", 0).edit().putBoolean("gateway_on", true).apply();
                }
                if (z) {
                    if (MainActivity.this.mpin.length() < 4) {
                        MainActivity.this.buttonClick.setChecked(false);
                        Toast.makeText(MainActivity.this, "Please Enter pin", Toast.LENGTH_LONG).show();
                        return;
                    } else if (MainActivity.this.url.length() < 4) {
                        MainActivity.this.buttonClick.setChecked(false);
                        Toast.makeText(MainActivity.this, "Please enter License key", Toast.LENGTH_LONG).show();
                        return;
                    } else {
                        // Show visual feedback during activation
                        showSwitchActivationFeedback(true);
                        MainActivity.this.check(0);
                        return;
                    }
                }
                MainActivity mainActivity = MainActivity.this;
                mainActivity.servname = "OFF";
                mainActivity.getSharedPreferences("pref", 0).edit().putBoolean("server_on", false).apply();
                SharedPreferences.Editor edit = MainActivity.this.getSharedPreferences("serv", 0).edit();
                edit.putInt("stop", 0);
                edit.commit();
                // Show visual feedback during deactivation
                showSwitchActivationFeedback(false);
                MainActivity.this.stopService();
            }
        });
        // Set click listeners only if the views are LinearLayouts
        if (linearLayout3 != null) {
            linearLayout3.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    MainActivity.this.startActivity(new Intent(MainActivity.this, (Class<?>) Monitoring.class));
                }
            });
        } else {
            // Alternative way to handle clicks for monView if it's not a LinearLayout
            monView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    MainActivity.this.startActivity(new Intent(MainActivity.this, (Class<?>) Monitoring.class));
                }
            });
        }

        if (linearLayout2 != null) {
            linearLayout2.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    MainActivity.this.startActivity(new Intent(MainActivity.this, com.appystore.mrecharge.activity.Settings.class));
                }
            });
        } else {
            // Alternative way to handle clicks for smsView if it's not a LinearLayout
            smsView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    MainActivity.this.startActivity(new Intent(MainActivity.this, com.appystore.mrecharge.activity.Settings.class));
                }
            });
        }

        if (linearLayout != null) {
            linearLayout.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    MainActivity.this.startActivity(new Intent(MainActivity.this, (Class<?>) intsetting.class));
                }
            });
        } else {
            // Alternative way to handle clicks for bankView if it's not a LinearLayout
            bankView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    MainActivity.this.startActivity(new Intent(MainActivity.this, (Class<?>) intsetting.class));
                }
            });
        }

        // Add a long click listener to test server connection
        this.buttonClick.setOnLongClickListener(new View.OnLongClickListener() {
            @Override
            public boolean onLongClick(View v) {
                // Validate activity state before showing dialog
                if (!isActivityValid()) {
                    Log.w(TAG, "Cannot show server test dialog - activity is not valid");
                    return true;
                }

                // Show confirmation dialog before testing
                AlertDialog.Builder builder = new AlertDialog.Builder(MainActivity.this);
                builder.setTitle("🔧 Test API Connection");
                builder.setMessage("Do you want to test the connection to the API server?");
                builder.setPositiveButton("Yes", new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialog, int which) {
                        if (isActivityValid()) {
                            // Test server connection
                            testServerConnection();
                        }
                    }
                });
                builder.setNegativeButton("No", null);
                showDialogSafely(builder, "server test confirmation");
                return true;
            }
        });
    }

    private void initializeExpirationManagement() {
        // Load saved expiration timestamp
        licenseExpirationTime = getPreference("expiration_timestamp", 0L);

        if (isActivated && licenseExpirationTime > 0) {
            // Check if license has already expired
            long currentTime = System.currentTimeMillis();
            if (currentTime >= licenseExpirationTime) {
                // License has expired, logout immediately
                handleLicenseExpired();
                return;
            }

            // Start expiration monitoring
            startExpirationMonitoring();
        }
    }

    private void handleLicenseExpired() {
        Log.w(TAG, "License has expired, logging out user");

        // Stop all monitoring
        stopExpirationMonitoring();
        stopApprovalStatusCheck();
        stopBlockStatusCheck();

        // Show expiration notification
        showLicenseExpiredNotification();

        // Clear all authentication data
        clearAuthenticationData();

        // Update UI state
        isActivated = false;
        isExpired = true;

        // Reset UI to activation screen
        resetUIToActivationState();

        // Show expiration dialog
        showLicenseExpiredDialog();
    }
    private void startExpirationMonitoring() {
        if (licenseExpirationTime <= 0) {
            return;
        }

        // Stop any existing monitoring
        stopExpirationMonitoring();

        // Show expiration section
        if (expirationSection != null) {
            expirationSection.setVisibility(View.VISIBLE);
        }

        // Start countdown timer
        startExpirationCountdown();

        // Start warning system
        startWarningSystem();

        Log.d(TAG, "Started expiration monitoring for: " + new java.util.Date(licenseExpirationTime));
    }

    /**
     * Stop expiration monitoring
     */
    private void stopExpirationMonitoring() {
        if (expirationCheckRunnable != null) {
            handler.removeCallbacks(expirationCheckRunnable);
            expirationCheckRunnable = null;
        }

        if (warningNotificationRunnable != null) {
            handler.removeCallbacks(warningNotificationRunnable);
            warningNotificationRunnable = null;
        }

        // Hide expiration section
        if (expirationSection != null) {
            expirationSection.setVisibility(View.GONE);
        }

        // Clear notifications
        clearExpirationNotifications();
    }

    /**
     * Start expiration countdown timer
     */
    private void startExpirationCountdown() {
        expirationCheckRunnable = new Runnable() {
            @Override
            public void run() {
                updateExpirationDisplay();

                // Check if expired
                long currentTime = System.currentTimeMillis();
                if (currentTime >= licenseExpirationTime) {
                    handleLicenseExpired();
                    return;
                }

                // Schedule next update
                handler.postDelayed(this, EXPIRATION_CHECK_INTERVAL);
            }
        };

        // Start immediately
        handler.post(expirationCheckRunnable);
    }

    /**
     * Update expiration display in real-time
     */
    private void updateExpirationDisplay() {
        if (licenseExpirationTime <= 0) {
            return;
        }

        long currentTime = System.currentTimeMillis();
        long timeRemaining = licenseExpirationTime - currentTime;

        if (timeRemaining <= 0) {
            // Expired
            if (expirationText != null) {
                expirationText.setText("License Expired");
                expirationText.setTextColor(Color.RED);
            }
            if (countdownText != null) {
                countdownText.setText("Please renew your license");
                countdownText.setTextColor(Color.RED);
            }
            if (expirationProgressBar != null) {
                expirationProgressBar.setProgress(0);
            }
            return;
        }

        // Calculate time components including seconds
        long days = timeRemaining / (24 * 60 * 60 * 1000);
        long hours = (timeRemaining % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000);
        long minutes = (timeRemaining % (60 * 60 * 1000)) / (60 * 1000);
        long seconds = (timeRemaining % (60 * 1000)) / 1000;

        // Format expiration date with 12-hour time format
        java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("MMM dd, yyyy 'at' hh:mm:ss a", java.util.Locale.getDefault());
        String expirationDate = sdf.format(new java.util.Date(licenseExpirationTime));

        // Update UI
        if (expirationText != null) {
            expirationText.setText("License expires: " + expirationDate);
        }

        if (countdownText != null) {
            String countdownStr;
            if (days > 0) {
                countdownStr = String.format(java.util.Locale.getDefault(),
                        "%d days, %d hours, %d minutes, %d seconds remaining", days, hours, minutes, seconds);
            } else if (hours > 0) {
                countdownStr = String.format(java.util.Locale.getDefault(),
                        "%d hours, %d minutes, %d seconds remaining", hours, minutes, seconds);
            } else if (minutes > 0) {
                countdownStr = String.format(java.util.Locale.getDefault(),
                        "%d minutes, %d seconds remaining", minutes, seconds);
            } else {
                countdownStr = String.format(java.util.Locale.getDefault(),
                        "%d seconds remaining", seconds);
            }
            countdownText.setText(countdownStr);
        }

        // Update colors based on time remaining
        int textColor;
        if (timeRemaining <= FINAL_WARNING_PERIOD_MS) {
            textColor = Color.RED; // Final 24 hours
        } else if (timeRemaining <= WARNING_PERIOD_MS) {
            textColor = Color.parseColor("#FF8C00"); // Warning period (orange)
        } else {
            textColor = Color.parseColor("#4CAF50"); // Normal (green)
        }

        if (expirationText != null) {
            expirationText.setTextColor(textColor);
        }
        if (countdownText != null) {
            countdownText.setTextColor(textColor);
        }

        // Update progress bar (assuming 30 days total license period)
        if (expirationProgressBar != null) {
            long totalLicensePeriod = 30 * 24 * 60 * 60 * 1000L; // 30 days
            int progress = (int) ((timeRemaining * 100) / totalLicensePeriod);
            progress = Math.max(0, Math.min(100, progress));
            expirationProgressBar.setProgress(progress);
        }
    }

    /**
     * Start warning system for license expiration
     */
    private void startWarningSystem() {
        warningNotificationRunnable = new Runnable() {
            @Override
            public void run() {
                long currentTime = System.currentTimeMillis();
                long timeRemaining = licenseExpirationTime - currentTime;

                // Check if we're in warning period
                if (timeRemaining <= WARNING_PERIOD_MS && timeRemaining > 0) {
                    isInWarningPeriod = true;

                    // Show warning notification every 5 minutes
                    if (currentTime - lastWarningTime >= WARNING_NOTIFICATION_INTERVAL) {
                        showExpirationWarning(timeRemaining);
                        lastWarningTime = currentTime;
                    }

                    // Schedule next check
                    handler.postDelayed(this, WARNING_NOTIFICATION_INTERVAL);
                } else if (timeRemaining <= 0) {
                    // License expired
                    handleLicenseExpired();
                } else {
                    // Not in warning period yet, check again later
                    long nextCheckDelay = timeRemaining - WARNING_PERIOD_MS;
                    nextCheckDelay = Math.min(nextCheckDelay, 60 * 60 * 1000L); // Max 1 hour
                    handler.postDelayed(this, nextCheckDelay);
                }
            }
        };

        // Start immediately
        handler.post(warningNotificationRunnable);
    }

    /**
     * Show expiration warning notification and dialog
     */
    private void showExpirationWarning(long timeRemaining) {
        // Calculate time components including seconds
        long days = timeRemaining / (24 * 60 * 60 * 1000);
        long hours = (timeRemaining % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000);
        long minutes = (timeRemaining % (60 * 60 * 1000)) / (60 * 1000);
        long seconds = (timeRemaining % (60 * 1000)) / 1000;

        String timeRemainingStr;
        if (days > 0) {
            timeRemainingStr = String.format(java.util.Locale.getDefault(),
                    "%d days, %d hours", days, hours);
        } else if (hours > 0) {
            timeRemainingStr = String.format(java.util.Locale.getDefault(),
                    "%d hours, %d minutes", hours, minutes);
        } else if (minutes > 0) {
            timeRemainingStr = String.format(java.util.Locale.getDefault(),
                    "%d minutes, %d seconds", minutes, seconds);
        } else {
            timeRemainingStr = String.format(java.util.Locale.getDefault(),
                    "%d seconds", seconds);
        }

        // Show system notification
        showExpirationNotification(timeRemainingStr, timeRemaining <= FINAL_WARNING_PERIOD_MS);

        // Show in-app warning dialog
        showExpirationWarningDialog(timeRemainingStr, timeRemaining <= FINAL_WARNING_PERIOD_MS);
    }

    /**
     * Show system notification for expiration warning
     */
    private void showExpirationNotification(String timeRemaining, boolean isFinalWarning) {
        // Check if notifications are enabled before showing
        if (!areNotificationsEnabled()) {
            Log.w(TAG, "Cannot show notification - permission not granted");
            return;
        }

        String title = isFinalWarning ? "License Expiring Soon!" : "License Expiration Warning";
        String message = "Your license expires in " + timeRemaining + ". Please renew to continue using the app.";

        Intent intent = new Intent(this, MainActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        PendingIntent pendingIntent = PendingIntent.getActivity(this, 0, intent,
                PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE);

        NotificationCompat.Builder builder = new NotificationCompat.Builder(this, NOTIFICATION_CHANNEL_ID)
                .setSmallIcon(android.R.drawable.ic_dialog_alert)
                .setContentTitle(title)
                .setContentText(message)
                .setStyle(new NotificationCompat.BigTextStyle().bigText(message))
                .setPriority(NotificationCompat.PRIORITY_HIGH)
                .setContentIntent(pendingIntent)
                .setAutoCancel(false)
                .setOngoing(isFinalWarning) // Make non-dismissible during final 24 hours
                .addAction(android.R.drawable.ic_menu_view, "Renew License", pendingIntent);

        if (isFinalWarning) {
            builder.setColor(Color.RED);
            builder.setLights(Color.RED, 1000, 1000);
        } else {
            builder.setColor(Color.parseColor("#FF8C00"));
        }

        try {
            NotificationManagerCompat notificationManager = NotificationManagerCompat.from(this);
            notificationManager.notify(NOTIFICATION_ID_WARNING, builder.build());
            Log.d(TAG, "Expiration notification shown successfully");
        } catch (SecurityException e) {
            Log.e(TAG, "Failed to show notification due to security exception", e);
        }
    }

    /**
     * Show in-app warning dialog
     */
    private void showExpirationWarningDialog(String timeRemaining, boolean isFinalWarning) {
        String title = isFinalWarning ? "⚠️ License Expiring Soon!" : "📅 License Expiration Notice";
        String message = "Your license will expire in " + timeRemaining + ".\n\n" +
                "Please renew your license to continue using the app without interruption.";

        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle(title);
        builder.setMessage(message);
        builder.setPositiveButton("Renew License", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                // Handle license renewal (could open a web page or contact form)
                showRenewalOptions();
            }
        });

        if (!isFinalWarning) {
            builder.setNegativeButton("Remind Later", null);
        }

        builder.setCancelable(!isFinalWarning); // Non-dismissible during final 24 hours

        AlertDialog dialog = builder.create();
        dialog.show();

        // Style the dialog based on warning level
        if (isFinalWarning) {
            dialog.getButton(AlertDialog.BUTTON_POSITIVE).setTextColor(Color.RED);
        }
    }

    /**
     * Show renewal options to the user
     */
    private void showRenewalOptions() {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle("Renew License");
        builder.setMessage("To renew your license, please contact support or visit our website.\n\n" +
                "Would you like to:");

        builder.setPositiveButton("Contact Support", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                // Could open email app or phone dialer
                Toast.makeText(MainActivity.this, "Please contact support for license renewal", Toast.LENGTH_LONG).show();
            }
        });

        builder.setNegativeButton("Enter New License", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                // Clear current license and show activation screen
                handleLicenseExpired();
            }
        });

        builder.setNeutralButton("Cancel", null);
        builder.show();
    }


    /**
     * Show license expired notification
     */
    private void showLicenseExpiredNotification() {
        // Check if notifications are enabled before showing
        if (!areNotificationsEnabled()) {
            Log.w(TAG, "Cannot show expired notification - permission not granted");
            return;
        }

        String title = "License Expired";
        String message = "Your license has expired. Please renew to continue using the app.";

        Intent intent = new Intent(this, MainActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        PendingIntent pendingIntent = PendingIntent.getActivity(this, 0, intent,
                PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE);

        NotificationCompat.Builder builder = new NotificationCompat.Builder(this, NOTIFICATION_CHANNEL_ID)
                .setSmallIcon(android.R.drawable.ic_dialog_alert)
                .setContentTitle(title)
                .setContentText(message)
                .setPriority(NotificationCompat.PRIORITY_HIGH)
                .setContentIntent(pendingIntent)
                .setAutoCancel(true)
                .setColor(Color.RED);

        try {
            NotificationManagerCompat notificationManager = NotificationManagerCompat.from(this);
            notificationManager.notify(NOTIFICATION_ID_EXPIRED, builder.build());
            Log.d(TAG, "License expired notification shown successfully");
        } catch (SecurityException e) {
            Log.e(TAG, "Failed to show expired notification due to security exception", e);
        }
    }

    /**
     * Show license expired dialog
     */
    private void showLicenseExpiredDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle("🚫 License Expired");
        builder.setMessage("Your license has expired and you have been automatically logged out.\n\n" +
                "Please enter a new license key to continue using the app.");
        builder.setPositiveButton("Enter New License", null);
        builder.setCancelable(false);

        AlertDialog dialog = builder.create();
        dialog.show();

        dialog.getButton(AlertDialog.BUTTON_POSITIVE).setTextColor(Color.RED);
    }

    /**
     * Clear all authentication data
     */
    private void clearAuthenticationData() {
        // Clear all preferences related to authentication
        SharedPreferences prefs = PreferenceManager.getDefaultSharedPreferences(this);
        SharedPreferences.Editor editor = prefs.edit();

        editor.remove("is_activated");
        editor.remove("expires");
        editor.remove("expiration_timestamp");
        editor.remove("domain");
        editor.remove("pending_approval_log_id");
        editor.remove("server_license_key");

        // Hide domain login section
        hideDomainLoginSection();

        editor.apply();

        Log.d(TAG, "Cleared all authentication data");
    }

    /**
     * Reset UI to activation state
     */
    private void resetUIToActivationState() {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                // Switch back to license activation layout
                showLicenseActivation();

                // Enable input fields
                if (licenseKeyField != null) {
                    licenseKeyField.setEnabled(true);
                    licenseKeyField.setText("");
                }
                if (pinField != null) {
                    pinField.setEnabled(true);
                    pinField.setText("");
                }

                // Reset button
                if (activateButton != null) {
                    activateButton.setEnabled(true);
                    activateButton.setText("Activate License");
                }

                // Update status
                if (statusText != null) {
                    statusText.setText("License expired. Please enter a new license key to continue.");
                    statusText.setTextColor(Color.RED);
                }

                // Hide expiration section and domain login section
                if (expirationSection != null) {
                    expirationSection.setVisibility(View.GONE);
                }
                if (domainLoginSection != null) {
                    domainLoginSection.setVisibility(View.GONE);
                }

                // Re-enable EditText fields in main app content if they were disabled
                if (url != null) {
                    url.setEnabled(true);
                    url.setFocusable(true);
                    url.setClickable(true);
                }
                if (mpin != null) {
                    mpin.setEnabled(true);
                    mpin.setFocusable(true);
                    mpin.setClickable(true);
                }
            }
        });
    }

    /**
     * Clear expiration notifications
     */
    private void clearExpirationNotifications() {
        NotificationManagerCompat notificationManager = NotificationManagerCompat.from(this);
        notificationManager.cancel(NOTIFICATION_ID_WARNING);
        notificationManager.cancel(NOTIFICATION_ID_EXPIRED);
    }

    /**
     * Immediately stop all service requests when device is blocked
     */
    private void stopAllServiceRequests() {
        Log.w(TAG, "DEVICE BLOCKED: Immediately stopping all service requests");

        try {
            // 1. Stop the main service (sever.class) immediately
            stopService();
            Log.d(TAG, "Main service (sever) stopped due to device block");

            // 2. Update SharedPreferences to disable service
            SharedPreferences.Editor edit = getSharedPreferences("serv", 0).edit();
            edit.putInt("stop", 0); // Set stop flag to disable service operations
            edit.putInt("busy", 0); // Clear busy flag
            edit.putLong("ttime", 0); // Clear timeout
            edit.apply();
            Log.d(TAG, "Service preferences updated to stop state");

            // 3. Update server preferences to disable
            getSharedPreferences("pref", 0).edit().putBoolean("server_on", false).apply();
            Log.d(TAG, "Server preferences disabled");

            // 4. Stop any other background services that might be running
            stopOtherServices();

            // 5. Cancel any pending network requests
            cancelPendingNetworkRequests();

            Log.w(TAG, "All service requests stopped successfully due to device block");

        } catch (Exception e) {
            Log.e(TAG, "Error stopping service requests during device block", e);
        }
    }

    /**
     * Stop other background services
     */
    private void stopOtherServices() {
        try {
            // Stop International service if running
            if (isMyServiceRunning(com.appystore.mrecharge.service.International_service.class)) {
                Intent intent = new Intent(this, com.appystore.mrecharge.service.International_service.class);
                intent.putExtra("serv", "off");
                startService(intent);
                Log.d(TAG, "International service stopped");
            }

            // Stop SMS service if running
            if (isMyServiceRunning(com.appystore.mrecharge.service.Smsend.class)) {
                stopService(new Intent(this, com.appystore.mrecharge.service.Smsend.class));
                Log.d(TAG, "SMS service stopped");
            }

            // Stop USSD service if running
            if (isMyServiceRunning(USSDService.class)) {
                stopService(new Intent(this, USSDService.class));
                Log.d(TAG, "USSD service stopped");
            }

        } catch (Exception e) {
            Log.e(TAG, "Error stopping other services", e);
        }
    }

    /**
     * Cancel any pending network requests
     */
    private void cancelPendingNetworkRequests() {
        try {
            // Cancel Volley request queue if it exists
            if (requestQueue != null) {
                requestQueue.cancelAll(new RequestQueue.RequestFilter() {
                    @Override
                    public boolean apply(Request<?> request) {
                        return true; // Cancel all requests
                    }
                });
                Log.d(TAG, "All Volley requests cancelled");
            }

            // Cancel WorkManager requests
            if (mWorkManager != null) {
                mWorkManager.cancelAllWork();
                Log.d(TAG, "All WorkManager tasks cancelled");
            }

        } catch (Exception e) {
            Log.e(TAG, "Error cancelling network requests", e);
        }
    }

    /**
     * Disable the service toggle switch immediately
     */
    private void disableServiceToggleSwitch() {
        try {
            if (buttonClick != null) {
                // Disable the toggle switch
                buttonClick.setChecked(false);
                buttonClick.setEnabled(false);
                Log.d(TAG, "Service toggle switch disabled due to device block");

                // Show visual feedback
                Toast.makeText(this, "🚫 Service disabled - Device blocked", Toast.LENGTH_LONG).show();
            }
        } catch (Exception e) {
            Log.e(TAG, "Error disabling service toggle switch", e);
        }
    }

    /**
     * Check block status before starting service to prevent blocked devices from running
     */
    private void checkBlockStatusBeforeStartingService() {
        Log.d(TAG, "Checking block status before starting service");

        // Perform block status check in background thread
        new Thread(new Runnable() {
            @Override
            public void run() {
                HttpURLConnection connection = null;
                try {
                    // Create URL for block status check
                    URL url = new URL(getApiUrl());
                    connection = (HttpURLConnection) url.openConnection();

                    // Set request properties
                    connection.setRequestMethod("POST");
                    connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
                    connection.setRequestProperty("User-Agent", "LicenseActivate Android App");
                    connection.setConnectTimeout(10000);
                    connection.setReadTimeout(10000);
                    connection.setDoInput(true);
                    connection.setDoOutput(true);

                    // Create POST data for block status check
                    String deviceId = getUniqueDeviceId();
                    String postData = "action=check_block_status" +
                            "&device_id=" + URLEncoder.encode(deviceId, "UTF-8");

                    Log.d(TAG, "Pre-service block check for device: " + deviceId);

                    // Send POST data
                    try (DataOutputStream wr = new DataOutputStream(connection.getOutputStream())) {
                        wr.writeBytes(postData);
                        wr.flush();
                    }

                    // Get response
                    int responseCode = connection.getResponseCode();
                    if (responseCode == HttpURLConnection.HTTP_OK) {
                        // Read response
                        StringBuilder response = new StringBuilder();
                        try (BufferedReader reader = new BufferedReader(
                                new InputStreamReader(connection.getInputStream()))) {
                            String line;
                            while ((line = reader.readLine()) != null) {
                                response.append(line);
                            }
                        }

                        String responseData = response.toString();
                        Log.d(TAG, "Pre-service block check response: " + responseData);

                        // Process response on UI thread
                        final String finalResponse = responseData;
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                processPreServiceBlockCheck(finalResponse);
                            }
                        });
                    } else {
                        // If block check fails, start service anyway (fail-safe)
                        Log.w(TAG, "Block check failed with code: " + responseCode + ", starting service anyway");
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                startService();
                            }
                        });
                    }

                } catch (Exception e) {
                    Log.e(TAG, "Pre-service block check failed", e);
                    // If block check fails, start service anyway (fail-safe)
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            startService();
                        }
                    });
                } finally {
                    if (connection != null) {
                        connection.disconnect();
                    }
                }
            }
        }).start();
    }

    /**
     * Process block status response before starting service
     */
    private void processPreServiceBlockCheck(String responseData) {
        try {
            // Parse JSON response
            JSONArray jsonArray = new JSONArray(responseData);

            if (jsonArray.length() > 0) {
                JSONObject responseObj = jsonArray.getJSONObject(0);

                // Check both status field and is_blocked field
                int status = responseObj.optInt("status", 0);
                boolean isBlocked = responseObj.optBoolean("is_blocked", false);

                Log.d(TAG, "Pre-service block check - status: " + status + ", is_blocked: " + isBlocked);

                if (status == 4 || isBlocked) {
                    // Device is blocked - do NOT start service
                    Log.w(TAG, "Device is blocked, preventing service start - status: " + status + ", is_blocked: " + isBlocked);

                    // Disable the toggle switch
                    if (buttonClick != null) {
                        buttonClick.setChecked(false);
                    }

                    // Handle the block immediately
                    handleDeviceBlocked(responseObj);
                } else {
                    // Device is not blocked - safe to start service
                    Log.d(TAG, "Device is not blocked, starting service");
                    startService();
                }
            } else {
                // Empty response - start service anyway (fail-safe)
                Log.w(TAG, "Empty block check response, starting service anyway");
                startService();
            }
        } catch (JSONException e) {
            Log.e(TAG, "Error parsing pre-service block check response", e);
            // If parsing fails, start service anyway (fail-safe)
            startService();
        }
    }

    /**
     * Get long preference value
     */
    private long getPreference(String key, long defaultValue) {
        SharedPreferences prefs = PreferenceManager.getDefaultSharedPreferences(this);
        return prefs.getLong(key, defaultValue);
    }

    /**
     * Save long preference value
     */
    private void savePreference(String key, long value) {
        SharedPreferences prefs = PreferenceManager.getDefaultSharedPreferences(this);
        prefs.edit().putLong(key, value).apply();
    }

    /**
     * Check if the app is already activated
     */
    private void checkActivationStatus() {
        isActivated = getPreference("is_activated", false);

        // Load switch state
        loadSwitchState();

        if (isActivated) {
            updateUIForActivatedState();
            statusText.setText("App is activated and running");
            // Switch to main app content for already activated users
            showMainAppContent();
            // Start block status monitoring for already activated devices
            startBlockStatusCheck();
        } else {
            // Show license activation layout for non-activated users
            showLicenseActivation();
            // Check if we have a pending approval
            pendingApprovalLogId = getPreference("pending_approval_log_id", "");
            if (!pendingApprovalLogId.isEmpty()) {
                // Load approval pending state
                isApprovalPending = getPreference("approval_pending", false);
                statusText.setText("Approval pending... Checking status...");
                startApprovalStatusCheck();
            } else {
                statusText.setText("Please enter your license key and PIN to activate");
            }
        }
    }

    /**
     * Perform authentication with the server
     */
    private void performAuthentication() {
        String licenseKey = licenseKeyField.getText().toString().trim();
        String pin = pinField.getText().toString().trim();

        // Validate input
        if (licenseKey.isEmpty()) {
            Toast.makeText(this, "Please enter license key", Toast.LENGTH_SHORT).show();
            return;
        }

        if (pin.isEmpty()) {
            Toast.makeText(this, "Please enter PIN", Toast.LENGTH_SHORT).show();
            return;
        }

        // Save license key
        savePreference("license_key", licenseKey);

        // Disable button and show progress
        activateButton.setEnabled(false);
        statusText.setText("Authenticating...");

        // Perform authentication in background thread
        new Thread(new Runnable() {
            @Override
            public void run() {
                performAuthenticationRequest(licenseKey, pin);
            }
        }).start();
    }

    /**
     * Perform the actual authentication request to the server
     */
    private void performAuthenticationRequest(String licenseKey, String pin) {
        HttpURLConnection connection = null;
        try {
            // Create URL
            URL url = new URL(getApiUrl());
            connection = (HttpURLConnection) url.openConnection();

            // Set request properties
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
            connection.setRequestProperty("User-Agent", "LicenseActivate Android App");
            connection.setConnectTimeout(15000);
            connection.setReadTimeout(15000);
            connection.setDoInput(true);
            connection.setDoOutput(true);

            // Create POST data
            String deviceId = getUniqueDeviceId();
            String deviceInfo = getDeviceInfo();
            String postData = "license_key=" + URLEncoder.encode(licenseKey, "UTF-8") +
                    "&pin=" + URLEncoder.encode(pin, "UTF-8") +
                    "&device_id=" + URLEncoder.encode(deviceId, "UTF-8") +
                    "&device_info=" + URLEncoder.encode(deviceInfo, "UTF-8") +
                    "&type=server" +
                    "&pkg=premium" +
                    "&version=1" +
                    "&timestamp=" + System.currentTimeMillis();

            Log.d(TAG, "Sending authentication request to: " + url.toString());

            // Send POST data
            try (DataOutputStream wr = new DataOutputStream(connection.getOutputStream())) {
                wr.writeBytes(postData);
                wr.flush();
            }

            // Get response
            int responseCode = connection.getResponseCode();
            Log.d(TAG, "Response code: " + responseCode);

            // Read response
            StringBuilder response = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(
                    new InputStreamReader(
                            responseCode >= 400 ? connection.getErrorStream() : connection.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
            }

            String responseData = response.toString();
            Log.d(TAG, "Response data: " + responseData);

            // Process response on UI thread
            final String finalResponse = responseData;
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    processAuthenticationResponse(finalResponse);
                }
            });

        } catch (Exception e) {
            Log.e(TAG, "Authentication request failed", e);
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    handleAuthenticationError("Connection error: " + e.getMessage());
                }
            });
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }
    }

    /**
     * Process the authentication response from the server
     */
    private void processAuthenticationResponse(String responseData) {
        try {
            // Parse JSON response
            JSONArray jsonArray = new JSONArray(responseData);

            if (jsonArray.length() > 0) {
                JSONObject responseObj = jsonArray.getJSONObject(0);
                int status = responseObj.getInt("status");
                int version = responseObj.getInt("version");

                Log.d(TAG, "Response status: " + status + ", version: " + version);

                if (status == 1 && version == 1) {
                    // Authentication successful - approved
                    handleAuthenticationSuccess(responseObj);
                } else if (status == 2 && version == 1) {
                    // Authentication pending approval
                    handlePendingApproval(responseObj);
                } else if (status == 4 && version == 1) {
                    // Device is blocked
                    handleDeviceBlocked(responseObj);
                } else {
                    // Authentication failed
                    String message = responseObj.optString("message", "Authentication failed");
                    handleAuthenticationError(message);
                }
            } else {
                handleAuthenticationError("Empty response from server");
            }
        } catch (JSONException e) {
            Log.e(TAG, "Error parsing response", e);
            handleAuthenticationError("Invalid response format");
        }
    }

    /**
     * Handle successful authentication
     */
    private void handleAuthenticationSuccess(JSONObject responseObj) {
        try {
            // Save activation state
            savePreference("is_activated", true);
            isActivated = true;

            // Save domain and other settings if available
            if (responseObj.has("domain")) {
                String domain = responseObj.getString("domain");
                savePreference("domain", domain);

                // Show domain login section
                showDomainLoginSection(domain);
            }

            // Handle expiration date
            if (responseObj.has("expires")) {
                String expiresString = responseObj.getString("expires");
                savePreference("expires", expiresString);

                // Parse and save expiration timestamp
                long expirationTimestamp = parseExpirationDate(expiresString);
                if (expirationTimestamp > 0) {
                    licenseExpirationTime = expirationTimestamp;
                    savePreference("expiration_timestamp", licenseExpirationTime);

                    // Start expiration monitoring
                    startExpirationMonitoring();
                }
            }

            // Clear pending approval
            savePreference("pending_approval_log_id", "");
            pendingApprovalLogId = "";

            // Update approval pending state
            updateApprovalPendingState(false);

            // Update UI
            updateUIForActivatedState();
            statusText.setText("App activated successfully!");

            // Switch from license activation to main app content
            showMainAppContent();

            Toast.makeText(this, "License activated successfully!", Toast.LENGTH_LONG).show();

            // Start block status monitoring for activated devices
            startBlockStatusCheck();

            Log.d(TAG, "Authentication successful");

        } catch (JSONException e) {
            Log.e(TAG, "Error processing success response", e);
            handleAuthenticationError("Error processing response");
        }
    }


    /**
     * Parse expiration date string to timestamp
     */
    private long parseExpirationDate(String expiresString) {
        try {
            // Assuming the date format is YYYY-MM-DD
            // Convert to end of day (23:59:59)
            java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy-MM-dd", java.util.Locale.getDefault());
            java.util.Date date = sdf.parse(expiresString);
            if (date != null) {
                // Set to end of day
                java.util.Calendar cal = java.util.Calendar.getInstance();
                cal.setTime(date);
                cal.set(java.util.Calendar.HOUR_OF_DAY, 23);
                cal.set(java.util.Calendar.MINUTE, 59);
                cal.set(java.util.Calendar.SECOND, 59);
                cal.set(java.util.Calendar.MILLISECOND, 999);
                return cal.getTimeInMillis();
            }
        } catch (Exception e) {
            Log.e(TAG, "Error parsing expiration date: " + expiresString, e);
        }
        return 0;
    }

    private void showDomainLoginSection(String domain) {
        if (domainLoginSection != null && domainInfoText != null) {
            // Update domain info text
            domainInfoText.setText("Your domain: " + domain);

            // Show the domain login section
            domainLoginSection.setVisibility(View.VISIBLE);

            Log.d(TAG, "Domain login section shown for domain: " + domain);
        }
    }

    /**
     * Hide the domain login section
     */
    private void hideDomainLoginSection() {
        if (domainLoginSection != null) {
            domainLoginSection.setVisibility(View.GONE);
            Log.d(TAG, "Domain login section hidden");
        }
    }

    /**
     * Perform domain login when button is clicked
     */
    private void performDomainLogin() {
        String domain = getPreference("domain", "");

        if (domain.isEmpty()) {
            Toast.makeText(this, "❌ No domain found. Please reactivate your license.", Toast.LENGTH_LONG).show();
            Log.w(TAG, "Domain login attempted but no domain found");
            return;
        }

        // Show loading state
        showDomainLoginLoading(true);

        Log.d(TAG, "Starting domain login for: " + domain);

        // Perform domain login in background thread
        new Thread(new Runnable() {
            @Override
            public void run() {
                performDomainLoginRequest(domain);
            }
        }).start();
    }

    /**
     * Show/hide loading state for domain login button
     */
    private void showDomainLoginLoading(boolean isLoading) {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (domainLoginButton != null) {
                    domainLoginButton.setEnabled(!isLoading);
                    if (isLoading) {
                        domainLoginButton.setText("🔄 Opening...");
                    } else {
                        domainLoginButton.setText("🎛️ Open Dashboard");
                    }
                }
            }
        });
    }

    /**
     * Perform the actual domain login request
     */
    private void performDomainLoginRequest(String domain) {
        try {
            // Construct the domain URL
            String domainUrl = constructDomainUrl(domain);

            Log.d(TAG, "Attempting to launch dashboard for domain: " + domainUrl);

            // Launch DashboardActivity with authentication data
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                   launchDashboardActivity(domainUrl);
                }
            });

        } catch (Exception e) {
            Log.e(TAG, "Domain login failed", e);
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    showDomainLoginLoading(false);
                    handleDomainLoginError("Failed to launch dashboard: " + e.getMessage());
                }
            });
        }
    }

    private void handleDomainLoginError(String errorMessage) {
        Toast.makeText(this, "❌ " + errorMessage, Toast.LENGTH_LONG).show();

        // Show error dialog with options
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle("🎛️ Dashboard Access Error");
        builder.setMessage(errorMessage + "\n\nWould you like to:");

        builder.setPositiveButton("Try Again", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                performDomainLogin();
            }
        });

        builder.setNegativeButton("Copy Domain", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                copyDomainToClipboard();
            }
        });

        builder.setNeutralButton("Cancel", null);
        builder.show();

        Log.e(TAG, "Dashboard access error: " + errorMessage);
    }

    /**
     * Copy domain to clipboard
     */
    private void copyDomainToClipboard() {
        String domain = getPreference("domain", "");
        if (!domain.isEmpty()) {
            android.content.ClipboardManager clipboard =
                    (android.content.ClipboardManager) getSystemService(Context.CLIPBOARD_SERVICE);
            android.content.ClipData clip = android.content.ClipData.newPlainText("Domain", domain);
            clipboard.setPrimaryClip(clip);

            Toast.makeText(this, "📋 Domain copied to clipboard: " + domain, Toast.LENGTH_LONG).show();
            Log.d(TAG, "Domain copied to clipboard: " + domain);
        }
    }


    /**
     * Construct the domain URL from the domain string
     */
    private String constructDomainUrl(String domain) {
        // Clean up the domain string
        String cleanDomain = domain.trim();

        // Add protocol if not present
        if (!cleanDomain.startsWith("http://") && !cleanDomain.startsWith("https://")) {
            cleanDomain = "https://" + cleanDomain;
        }

        // You can add specific paths or parameters here if needed
        // For example: cleanDomain + "/dashboard" or cleanDomain + "?app=mobile"

        return cleanDomain;
    }


    /**
     * Handle pending approval state
     */
    private void handlePendingApproval(JSONObject responseObj) {
        try {
            String message = responseObj.optString("message",
                    "Your device is pending approval by the administrator. Please wait...");

            // Save log ID for future status checks
            if (responseObj.has("log_id")) {
                pendingApprovalLogId = responseObj.getString("log_id");
                savePreference("pending_approval_log_id", pendingApprovalLogId);
            }

            // Set approval pending state for switch functionality
            updateApprovalPendingState(true);

            // Update UI
            activateButton.setEnabled(true);
            statusText.setText(message);

            // Show pending approval dialog
            showPendingApprovalDialog(message);

            // Start checking approval status
            startApprovalStatusCheck();

            Log.d(TAG, "Authentication pending approval, log_id: " + pendingApprovalLogId);

        } catch (JSONException e) {
            Log.e(TAG, "Error processing pending approval response", e);
            handleAuthenticationError("Error processing response");
        }
    }

    /**
     * Handle device blocked response with thread-safe UI operations
     */
    private void handleDeviceBlocked(JSONObject responseObj) {
        final String message = responseObj.optString("message", "Your device has been blocked");
        final String blockReason = responseObj.optString("block_reason", "No reason provided");
        final String blockedAt = responseObj.optString("blocked_at", "Unknown");

        Log.w(TAG, "Device blocked: " + message + ", Reason: " + blockReason);

        // IMMEDIATELY stop all service requests before any UI operations
        stopAllServiceRequests();

        // Ensure UI operations are performed on the main thread
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                // Validate activity state before proceeding
                if (!isActivityValid()) {
                    Log.w(TAG, "Cannot handle device blocked - activity is not valid");
                    return;
                }

                try {
                    // Stop all monitoring and clear data
                    stopApprovalStatusCheck();
                    stopBlockStatusCheck();
                    stopExpirationMonitoring();

                    // Disable the service toggle switch immediately
                    disableServiceToggleSwitch();

                    // Clear all authentication data
                    clearAuthenticationData();

                    // Update UI state
                    isActivated = false;
                    resetUIToActivationState();

                    // Show blocked device dialog with validation
                    showDeviceBlockedDialog(message, blockReason, blockedAt);
                } catch (Exception e) {
                    Log.e(TAG, "Error handling device blocked response", e);
                    // Fallback: show toast if dialog operations fail
                    if (isActivityValid()) {
                        Toast.makeText(MainActivity.this, "Device blocked: " + message, Toast.LENGTH_LONG).show();
                    }
                }
            }
        });
    }

    /**
     * Handle authentication error
     */
    private void handleAuthenticationError(String errorMessage) {
        activateButton.setEnabled(true);
        statusText.setText("Authentication failed");

        Toast.makeText(this, errorMessage, Toast.LENGTH_LONG).show();

        Log.e(TAG, "Authentication error: " + errorMessage);
    }

    /**
     * Show pending approval dialog with activity validation
     */
    private void showPendingApprovalDialog(String message) {
        if (!isActivityValid()) {
            Log.w(TAG, "Cannot show pending approval dialog - activity is not valid");
            return;
        }

        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle("⏳ Approval Pending");
        builder.setMessage(message + "\n\nWe will automatically check for approval every 10 seconds.\n\n" +
                "Your request is being reviewed by an administrator.");
        builder.setPositiveButton("OK", null);
        builder.setNegativeButton("Check Now", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                if (isActivityValid()) {
                    checkApprovalStatusNow();
                }
            }
        });
        showDialogSafely(builder, "pending approval");
    }

    /**
     * Start periodic approval status checking
     */
    private void startApprovalStatusCheck() {
        if (pendingApprovalLogId == null || pendingApprovalLogId.isEmpty()) {
            return;
        }

        approvalCheckCount = 0;

        approvalCheckRunnable = new Runnable() {
            @Override
            public void run() {
                if (approvalCheckCount < MAX_APPROVAL_CHECKS && !isActivated) {
                    checkApprovalStatusNow();
                    approvalCheckCount++;

                    // Schedule next check
                    handler.postDelayed(this, APPROVAL_CHECK_INTERVAL);
                } else {
                    // Stop checking after max attempts
                    if (!isActivated) {
                        statusText.setText("Approval check timeout. Please try again later.");
                    }
                }
            }
        };

        // Start first check after a short delay
        handler.postDelayed(approvalCheckRunnable, 2000);
    }

    /**
     * Stop approval status checking
     */
    private void stopApprovalStatusCheck() {
        if (approvalCheckRunnable != null) {
            handler.removeCallbacks(approvalCheckRunnable);
            approvalCheckRunnable = null;
        }
    }

    /**
     * Check approval status immediately
     */
    private void checkApprovalStatusNow() {
        if (pendingApprovalLogId == null || pendingApprovalLogId.isEmpty()) {
            return;
        }

        // Perform status check in background thread
        new Thread(new Runnable() {
            @Override
            public void run() {
                checkApprovalStatusRequest();
            }
        }).start();
    }

    /**
     * Perform approval status check request
     */
    private void checkApprovalStatusRequest() {
        HttpURLConnection connection = null;
        try {
            // Create URL for status check
            URL url = new URL(getApiUrl());
            connection = (HttpURLConnection) url.openConnection();

            // Set request properties
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
            connection.setRequestProperty("User-Agent", "LicenseActivate Android App");
            connection.setConnectTimeout(10000);
            connection.setReadTimeout(10000);
            connection.setDoInput(true);
            connection.setDoOutput(true);

            // Create POST data for status check
            String postData = "action=check_approval_status" +
                    "&log_id=" + URLEncoder.encode(pendingApprovalLogId, "UTF-8");

            Log.d(TAG, "Checking approval status for log_id: " + pendingApprovalLogId);

            // Send POST data
            try (DataOutputStream wr = new DataOutputStream(connection.getOutputStream())) {
                wr.writeBytes(postData);
                wr.flush();
            }

            // Get response
            int responseCode = connection.getResponseCode();

            // Read response
            StringBuilder response = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(
                    new InputStreamReader(
                            responseCode >= 400 ? connection.getErrorStream() : connection.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
            }

            String responseData = response.toString();
            Log.d(TAG, "Approval status response: " + responseData);

            // Process response on UI thread
            final String finalResponse = responseData;
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    processApprovalStatusResponse(finalResponse);
                }
            });

        } catch (Exception e) {
            Log.e(TAG, "Approval status check failed", e);
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    statusText.setText("Error checking approval status");
                }
            });
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }
    }

    /**
     * Process approval status response
     */
    private void processApprovalStatusResponse(String responseData) {
        try {
            JSONArray jsonArray = new JSONArray(responseData);

            if (jsonArray.length() > 0) {
                JSONObject responseObj = jsonArray.getJSONObject(0);
                int status = responseObj.getInt("status");

                if (status == 1) {
                    // Approved! Stop checking and activate
                    stopApprovalStatusCheck();
                    handleAuthenticationSuccess(responseObj);
                } else if (status == 3) {
                    // Rejected
                    stopApprovalStatusCheck();
                    savePreference("pending_approval_log_id", "");
                    pendingApprovalLogId = "";

                    String message = responseObj.optString("message", "Your request has been rejected");
                    handleAuthenticationError(message);
                } else if (status == 4) {
                    // Device is blocked
                    stopApprovalStatusCheck();
                    handleDeviceBlocked(responseObj);
                } else {
                    // Still pending, continue checking
                    statusText.setText("Still pending approval... (" + approvalCheckCount + "/" + MAX_APPROVAL_CHECKS + ")");
                }
            }
        } catch (JSONException e) {
            Log.e(TAG, "Error parsing approval status response", e);
        }
    }

    // ==================== BLOCK STATUS CHECKING METHODS ====================

    /**
     * Start periodic block status checking for activated devices
     */
    private void startBlockStatusCheck() {
        if (!isActivated) {
            return;
        }

        blockStatusRunnable = new Runnable() {
            @Override
            public void run() {
                if (isActivated) {
                    checkBlockStatusNow();
                    // Schedule next check
                    handler.postDelayed(this, BLOCK_CHECK_INTERVAL);
                }
            }
        };

        // Start first check after a short delay
        handler.postDelayed(blockStatusRunnable, 5000);
        Log.d(TAG, "Block status monitoring started");
    }

    /**
     * Stop block status checking
     */
    private void stopBlockStatusCheck() {
        if (blockStatusRunnable != null) {
            handler.removeCallbacks(blockStatusRunnable);
            blockStatusRunnable = null;
            Log.d(TAG, "Block status monitoring stopped");
        }
    }

    /**
     * Check block status immediately
     */
    private void checkBlockStatusNow() {
        // Perform block status check in background thread
        new Thread(new Runnable() {
            @Override
            public void run() {
                checkBlockStatusRequest();
            }
        }).start();
    }

    /**
     * Perform block status check request
     */
    private void checkBlockStatusRequest() {
        HttpURLConnection connection = null;
        try {
            // Create URL for block status check
            URL url = new URL(getApiUrl());
            connection = (HttpURLConnection) url.openConnection();

            // Set request properties
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
            connection.setRequestProperty("User-Agent", "LicenseActivate Android App");
            connection.setConnectTimeout(10000);
            connection.setReadTimeout(10000);
            connection.setDoInput(true);
            connection.setDoOutput(true);

            // Create POST data for block status check
            String deviceId = getUniqueDeviceId();
            String postData = "action=check_block_status" +
                    "&device_id=" + URLEncoder.encode(deviceId, "UTF-8");

            Log.d(TAG, "Checking block status for device: " + deviceId);

            // Send POST data
            try (DataOutputStream wr = new DataOutputStream(connection.getOutputStream())) {
                wr.writeBytes(postData);
                wr.flush();
            }

            // Get response
            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                // Read response
                StringBuilder response = new StringBuilder();
                try (BufferedReader reader = new BufferedReader(
                        new InputStreamReader(connection.getInputStream()))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        response.append(line);
                    }
                }

                String responseData = response.toString();
                Log.d(TAG, "Block status response: " + responseData);

                // Process response on UI thread
                final String finalResponse = responseData;
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        processBlockStatusResponse(finalResponse);
                    }
                });
            }

        } catch (Exception e) {
            Log.e(TAG, "Block status check failed", e);
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }
    }

    /**
     * Process block status response
     */
    private void processBlockStatusResponse(String responseData) {
        try {
            // Parse JSON response
            JSONArray jsonArray = new JSONArray(responseData);

            if (jsonArray.length() > 0) {
                JSONObject responseObj = jsonArray.getJSONObject(0);

                // Check both status field and is_blocked field for comprehensive block detection
                int status = responseObj.optInt("status", 0);
                boolean isBlocked = responseObj.optBoolean("is_blocked", false);

                Log.d(TAG, "Block status check - status: " + status + ", is_blocked: " + isBlocked);

                if (status == 4 || isBlocked) {
                    // Device is blocked - stop all services immediately
                    Log.w(TAG, "Device block detected - status: " + status + ", is_blocked: " + isBlocked);
                    handleDeviceBlocked(responseObj);
                }
                // If status is 1 and is_blocked is false, device is not blocked - continue normal operation
            }
        } catch (JSONException e) {
            Log.e(TAG, "Error parsing block status response", e);
        }
    }

    /**
     * Show device blocked dialog with proper activity lifecycle validation
     */
    private void showDeviceBlockedDialog(String message, String blockReason, String blockedAt) {
        // Validate activity state before showing dialog
        if (!isActivityValid()) {
            Log.w(TAG, "Cannot show device blocked dialog - activity is not valid");
            return;
        }

        try {
            AlertDialog.Builder builder = new AlertDialog.Builder(this);
            builder.setTitle("🚫 Device Blocked");
            builder.setMessage(message + "\n\n" +
                    "Reason: " + blockReason + "\n" +
                    "Blocked at: " + blockedAt + "\n\n" +
                    "Please contact the administrator for assistance.");

            builder.setPositiveButton("OK", new DialogInterface.OnClickListener() {
                @Override
                public void onClick(DialogInterface dialog, int which) {
                    // Close the app or return to login screen
                    if (isActivityValid()) {
                        finish();
                    }
                }
            });

            builder.setCancelable(false);

            // Final validation before showing
            if (isActivityValid()) {
                AlertDialog dialog = builder.create();
                dialog.show();
                Log.w(TAG, "Device blocked dialog shown successfully");
            } else {
                Log.w(TAG, "Activity became invalid before showing dialog");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error showing device blocked dialog", e);
            // Fallback: show toast message if dialog fails
            if (isActivityValid()) {
                Toast.makeText(this, "Device blocked: " + message, Toast.LENGTH_LONG).show();
            }
        }
    }

    /**
     * Validate if activity is in a valid state for UI operations
     * This prevents WindowManager$BadTokenException when showing dialogs
     */
    private boolean isActivityValid() {
        if (isFinishing()) {
            Log.d(TAG, "Activity is finishing - cannot perform UI operations");
            return false;
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR1 && isDestroyed()) {
            Log.d(TAG, "Activity is destroyed - cannot perform UI operations");
            return false;
        }

        return true;
    }

    /**
     * Show dialog safely with activity lifecycle validation
     */
    private void showDialogSafely(AlertDialog.Builder builder, String dialogType) {
        if (!isActivityValid()) {
            Log.w(TAG, "Cannot show " + dialogType + " dialog - activity is not valid");
            return;
        }

        try {
            if (isActivityValid()) {
                AlertDialog dialog = builder.create();
                dialog.show();
                Log.d(TAG, dialogType + " dialog shown successfully");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error showing " + dialogType + " dialog", e);
        }
    }

    // ==================== SWITCH/TOGGLE FUNCTIONALITY METHODS ====================

    /**
     * Show approval pending status when switch is toggled during pending state
     */
    private void showApprovalPendingStatus() {
        if (!isActivityValid()) {
            return;
        }

        Toast.makeText(this, "⏳ Waiting for approval... Switch will enable automatically when approved",
                Toast.LENGTH_LONG).show();

        // Update status text if available
        if (statusText != null) {
            statusText.setText("⏳ Waiting for approval - Switch will enable automatically");
            statusText.setTextColor(Color.parseColor("#FF8C00")); // Orange color
        }

        Log.d(TAG, "Switch set to auto-enable on approval");
    }

    /**
     * Show visual feedback during switch activation/deactivation
     */
    private void showSwitchActivationFeedback(boolean isActivating) {
        if (!isActivityValid()) {
            return;
        }

        String message = isActivating ? "🔄 Activating service..." : "🔄 Deactivating service...";
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();

        // Update status text if available
        if (statusText != null) {
            statusText.setText(message);
            statusText.setTextColor(isActivating ? Color.parseColor("#4CAF50") : Color.parseColor("#FF5722"));
        }

        Log.d(TAG, "Switch feedback: " + (isActivating ? "activating" : "deactivating"));
    }

    /**
     * Auto-enable switch when approval is granted
     */
    private void autoEnableSwitchOnApproval() {
        if (!switchAutoEnableOnApproval) {
            return;
        }

        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                if (!isActivityValid()) {
                    return;
                }

                try {
                    // Enable the switch
                    if (buttonClick != null) {
                        buttonClick.setChecked(true);

                        // Show success feedback
                        Toast.makeText(MainActivity.this, "✅ Approved! Service activated automatically",
                                Toast.LENGTH_LONG).show();

                        // Update status text
                        if (statusText != null) {
                            statusText.setText("✅ Approved and activated successfully");
                            statusText.setTextColor(Color.parseColor("#4CAF50")); // Green color
                        }

                        // Clear the auto-enable flag
                        switchAutoEnableOnApproval = false;
                        savePreference("switch_auto_enable", false);

                        Log.d(TAG, "Switch auto-enabled after approval");
                    }
                } catch (Exception e) {
                    Log.e(TAG, "Error auto-enabling switch", e);
                }
            }
        });
    }

    /**
     * Load switch state from SharedPreferences
     */
    private void loadSwitchState() {
        switchAutoEnableOnApproval = getPreference("switch_auto_enable", false);
        Log.d(TAG, "Loaded switch auto-enable state: " + switchAutoEnableOnApproval);
    }

    /**
     * Update approval pending state
     */
    private void updateApprovalPendingState(boolean isPending) {
        isApprovalPending = isPending;
        savePreference("approval_pending", isPending);

        if (isPending) {
            Log.d(TAG, "Approval state set to pending");
        } else {
            Log.d(TAG, "Approval state cleared");
            // If approval is no longer pending and auto-enable is set, enable the switch
            if (switchAutoEnableOnApproval) {
                autoEnableSwitchOnApproval();
            }
        }
    }

    /**
     * Update UI for activated state
     */
    private void updateUIForActivatedState() {
        activateButton.setEnabled(false);
        activateButton.setText("Activated");
        licenseKeyField.setEnabled(false);
        pinField.setEnabled(false);

        // Stop any pending approval checks
        stopApprovalStatusCheck();

        // Show domain login section if domain is available
        String savedDomain = getPreference("domain", "");
        if (!savedDomain.isEmpty()) {
            showDomainLoginSection(savedDomain);
        }
    }


    /**
     * Get device ID (Android ID)
     */
    private String getUniqueDeviceId() {
        return Settings.Secure.getString(getContentResolver(), Settings.Secure.ANDROID_ID);
    }

    /**
     * Get device information
     */
    private String getDeviceInfo() {
        return Build.MANUFACTURER + " " + Build.MODEL + " (Android " + Build.VERSION.RELEASE + ")";
    }

    /**
     * Save string preference
     */
    private void savePreference(String key, String value) {
        SharedPreferences prefs = PreferenceManager.getDefaultSharedPreferences(this);
        prefs.edit().putString(key, value).apply();
    }

    /**
     * Save boolean preference
     */


    /**
     * Get string preference
     */
    private String getPreference(String key, String defaultValue) {
        SharedPreferences prefs = PreferenceManager.getDefaultSharedPreferences(this);
        return prefs.getString(key, defaultValue);
    }



    private void initializeUI() {
        licenseKeyField = findViewById(R.id.licenseKeyField);
        pinField = findViewById(R.id.pinField);
        activateButton = findViewById(R.id.activateButton);
        statusText = findViewById(R.id.statusText);
        expirationSection = findViewById(R.id.expirationSection);
        expirationText = findViewById(R.id.expirationText);
        countdownText = findViewById(R.id.countdownText);
        expirationProgressBar = findViewById(R.id.expirationProgressBar);

        // Initialize domain login UI components
        domainLoginSection = findViewById(R.id.domainLoginSection);
        domainInfoText = findViewById(R.id.domainInfoText);
        domainLoginButton = findViewById(R.id.domainLoginButton);

        // Initialize layout containers for visibility management
        licenseActivationLayout = findViewById(R.id.license_activation);
        btnSpeakContainer = findViewById(R.id.btnSpeakContainer);
        mainAppContent = findViewById(R.id.mainAppContent);

        // Set up activate button click listener
        activateButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                performAuthentication();
            }
        });

        // Set up domain login button click listener
        domainLoginButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                performDomainLogin();
            }
        });

        // Load saved license key if available
        String savedLicenseKey = getPreference("license_key", "");
        if (!savedLicenseKey.isEmpty()) {
            licenseKeyField.setText(savedLicenseKey);
        }

        // Initially hide expiration section and domain login section
        if (expirationSection != null) {
            expirationSection.setVisibility(View.GONE);
        }
        if (domainLoginSection != null) {
            domainLoginSection.setVisibility(View.GONE);
        }

        // Set initial layout visibility based on activation status
        updateLayoutVisibility();
    }

    /**
     * Update layout visibility based on activation status
     */
    private void updateLayoutVisibility() {
        if (isActivated) {
            // Show main app content, hide license activation
            showMainAppContent();
        } else {
            // Show license activation, hide main app content
            showLicenseActivation();
        }
    }

    /**
     * Show license activation layout and hide main app content
     */
    private void showLicenseActivation() {
        if (licenseActivationLayout != null) {
            licenseActivationLayout.setVisibility(View.VISIBLE);
        }
        if (btnSpeakContainer != null) {
            btnSpeakContainer.setVisibility(View.GONE);
        }
        if (mainAppContent != null) {
            mainAppContent.setVisibility(View.GONE);
        }
        Log.d(TAG, "Showing license activation layout");
    }

    /**
     * Show main app content and hide license activation
     */
    private void showMainAppContent() {
        if (licenseActivationLayout != null) {
            licenseActivationLayout.setVisibility(View.GONE);
        }
        if (btnSpeakContainer != null) {
            btnSpeakContainer.setVisibility(View.VISIBLE);
        }
        if (mainAppContent != null) {
            mainAppContent.setVisibility(View.VISIBLE);
        }

        // Auto-populate and disable EditText fields when showing main content
        populateAndDisableEditTextFields();

        Log.d(TAG, "Showing main app content");
    }

    /**
     * Auto-populate and disable EditText fields from SharedPreferences
     */
    private void populateAndDisableEditTextFields() {
        // Get saved values from SharedPreferences
        String savedLicenseKey = getPref("licence", getApplicationContext());
        String savedPin = getPrefx("pin", getApplicationContext());

        // Auto-populate URL field (license key)
        if (url != null && !TextUtils.isEmpty(savedLicenseKey)) {
            url.setText(savedLicenseKey);
            url.setEnabled(false); // Disable editing
            url.setFocusable(false);
            url.setClickable(false);
            Log.d(TAG, "Auto-populated URL field with saved license key");
        }

        // Auto-populate DPIN field
        if (mpin != null && !TextUtils.isEmpty(savedPin)) {
            mpin.setText(savedPin);
            mpin.setEnabled(false); // Disable editing
            mpin.setFocusable(false);
            mpin.setClickable(false);
            Log.d(TAG, "Auto-populated DPIN field with saved PIN");
        }
    }

    /**
     * Launch dashboard activity using data from SharedPreferences
     */
    private void launchDashboardActivity(String domainUrl) {
        try {
            // Create intent to open URL in browser
            Intent browserIntent = new Intent(Intent.ACTION_VIEW, Uri.parse(domainUrl));

            // Check if there's a browser available
            if (browserIntent.resolveActivity(getPackageManager()) != null) {
                startActivity(browserIntent);
                Log.d(TAG, "Launched dashboard in browser: " + domainUrl);

                // Show success message
                Toast.makeText(this, "🌐 Opening dashboard in browser...", Toast.LENGTH_SHORT).show();
            } else {
                // No browser available, show error
                Log.e(TAG, "No browser available to open URL: " + domainUrl);
                Toast.makeText(this, "❌ No browser available to open dashboard", Toast.LENGTH_LONG).show();
            }
        } catch (Exception e) {
            Log.e(TAG, "Error launching dashboard activity", e);
            Toast.makeText(this, "❌ Error opening dashboard: " + e.getMessage(), Toast.LENGTH_LONG).show();
        }
    }

    // ==================== NOTIFICATION PERMISSION METHODS ====================

    /**
     * Check and request notification permissions if needed
     */
    private void checkNotificationPermissions() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) { // API 33+
            if (ContextCompat.checkSelfPermission(this, Manifest.permission.POST_NOTIFICATIONS)
                    != PackageManager.PERMISSION_GRANTED) {

                // Check if we should show explanation
                if (ActivityCompat.shouldShowRequestPermissionRationale(this, Manifest.permission.POST_NOTIFICATIONS)) {
                    showNotificationPermissionExplanation();
                } else {
                    // Check if permission was previously requested and denied
                    boolean wasRequested = getPreference(NOTIFICATION_PERMISSION_REQUESTED_KEY, false);
                    if (wasRequested) {
                        // User denied permission permanently, show settings dialog
                        showNotificationPermissionSettingsDialog();
                    } else {
                        // First time requesting permission
                        requestNotificationPermission();
                    }
                }
            } else {
                Log.d(TAG, "Notification permission already granted");
            }
        } else {
            // For API < 33, notification permission is granted by default
            Log.d(TAG, "Notification permission not required for this API level");
        }
    }

    /**
     * Request notification permission
     */
    private void requestNotificationPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            savePreference(NOTIFICATION_PERMISSION_REQUESTED_KEY, true);
            ActivityCompat.requestPermissions(this,
                    new String[]{Manifest.permission.POST_NOTIFICATIONS},
                    NOTIFICATION_PERMISSION_REQUEST_CODE);
            Log.d(TAG, "Requesting notification permission");
        }
    }
    private void savePreference(String key, boolean value) {
        SharedPreferences prefs = PreferenceManager.getDefaultSharedPreferences(this);
        prefs.edit().putBoolean(key, value).apply();
    }

    /**
     * Get boolean preference
     */
    private boolean getPreference(String key, boolean defaultValue) {
        SharedPreferences prefs = PreferenceManager.getDefaultSharedPreferences(this);
        return prefs.getBoolean(key, defaultValue);
    }
    /**
     * Show explanation dialog for notification permission
     */
    private void showNotificationPermissionExplanation() {
        if (!isActivityValid()) {
            Log.w(TAG, "Cannot show notification permission explanation - activity is not valid");
            return;
        }

        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle("📱 Notification Permission Required");
        builder.setMessage("This app needs notification permission to:\n\n" +
                "• Alert you about license expiration warnings\n" +
                "• Notify you about important app updates\n" +
                "• Send real-time status notifications\n\n" +
                "This helps ensure you don't miss important information about your license status.");

        builder.setPositiveButton("Grant Permission", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                if (isActivityValid()) {
                    requestNotificationPermission();
                }
            }
        });

        builder.setNegativeButton("Not Now", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                if (isActivityValid()) {
                    Toast.makeText(MainActivity.this,
                            "You can enable notifications later in app settings",
                            Toast.LENGTH_LONG).show();
                }
            }
        });

        builder.setCancelable(false);
        showDialogSafely(builder, "notification permission explanation");
    }

    /**
     * Show dialog to redirect user to app settings for notification permission
     */
    private void showNotificationPermissionSettingsDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle("⚙️ Enable Notifications");
        builder.setMessage("To receive important license expiration warnings and app notifications, " +
                "please enable notifications in your device settings.\n\n" +
                "Go to: Settings > Apps > " + getString(R.string.app_name) + " > Notifications");

        builder.setPositiveButton("Open Settings", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                openAppNotificationSettings();
            }
        });

        builder.setNegativeButton("Skip", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                Toast.makeText(MainActivity.this,
                        "Notifications disabled. You may miss important license warnings.",
                        Toast.LENGTH_LONG).show();
            }
        });

        builder.show();
    }

    /**
     * Open app notification settings
     */
    private void openAppNotificationSettings() {
        Intent intent = new Intent();

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            intent.setAction(Settings.ACTION_APP_NOTIFICATION_SETTINGS);
            intent.putExtra(Settings.EXTRA_APP_PACKAGE, getPackageName());
        } else {
            intent.setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
            intent.setData(Uri.parse("package:" + getPackageName()));
        }

        try {
            startActivity(intent);
        } catch (Exception e) {
            Log.e(TAG, "Error opening notification settings", e);
            Toast.makeText(this, "Unable to open settings. Please enable notifications manually.",
                    Toast.LENGTH_LONG).show();
        }
    }

    /**
     * Check if notifications are enabled
     */
    private boolean areNotificationsEnabled() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            return ContextCompat.checkSelfPermission(this, Manifest.permission.POST_NOTIFICATIONS)
                    == PackageManager.PERMISSION_GRANTED;
        } else {
            // For API < 33, check if notifications are enabled via NotificationManagerCompat
            return NotificationManagerCompat.from(this).areNotificationsEnabled();
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);

        if (requestCode == NOTIFICATION_PERMISSION_REQUEST_CODE) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                Log.d(TAG, "Notification permission granted");
                Toast.makeText(this, "✅ Notifications enabled! You'll receive important license alerts.",
                        Toast.LENGTH_LONG).show();

                // Show a test notification to confirm it's working
                new Handler().postDelayed(new Runnable() {
                    @Override
                    public void run() {
                        showTestNotification();
                    }
                }, 1000); // Delay 1 second to let the toast finish
            } else {
                Log.w(TAG, "Notification permission denied");

                // Check if user denied permanently
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                    if (!ActivityCompat.shouldShowRequestPermissionRationale(this, Manifest.permission.POST_NOTIFICATIONS)) {
                        // User denied permanently
                        showNotificationPermissionDeniedDialog();
                    } else {
                        // User denied but can ask again
                        Toast.makeText(this,
                                "⚠️ Notifications disabled. You may miss important license warnings.",
                                Toast.LENGTH_LONG).show();
                    }
                }
            }
        }
    }

    /**
     * Show dialog when notification permission is permanently denied
     */
    private void showNotificationPermissionDeniedDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle("🚫 Notifications Disabled");
        builder.setMessage("Notifications have been disabled. You will not receive:\n\n" +
                "• License expiration warnings\n" +
                "• Important app alerts\n" +
                "• Real-time status updates\n\n" +
                "You can enable them anytime in app settings.");

        builder.setPositiveButton("Enable in Settings", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                openAppNotificationSettings();
            }
        });

        builder.setNegativeButton("Continue Without", null);
        builder.show();
    }

    /**
     * Show a test notification to verify permission is working
     */
    public void showTestNotification() {
        if (!areNotificationsEnabled()) {
            Toast.makeText(this, "Notification permission not granted", Toast.LENGTH_SHORT).show();
            return;
        }

        String title = "✅ Notifications Enabled";
        String message = "Great! You'll receive important license alerts and app notifications.";

        Intent intent = new Intent(this, MainActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        PendingIntent pendingIntent = PendingIntent.getActivity(this, 0, intent,
                PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE);

        NotificationCompat.Builder builder = new NotificationCompat.Builder(this, NOTIFICATION_CHANNEL_ID)
                .setSmallIcon(android.R.drawable.ic_dialog_info)
                .setContentTitle(title)
                .setContentText(message)
                .setStyle(new NotificationCompat.BigTextStyle().bigText(message))
                .setPriority(NotificationCompat.PRIORITY_DEFAULT)
                .setContentIntent(pendingIntent)
                .setAutoCancel(true)
                .setColor(Color.parseColor("#4CAF50"));

        try {
            NotificationManagerCompat notificationManager = NotificationManagerCompat.from(this);
            notificationManager.notify(9999, builder.build()); // Use unique ID for test notification
            Log.d(TAG, "Test notification shown successfully");
        } catch (SecurityException e) {
            Log.e(TAG, "Failed to show test notification", e);
            Toast.makeText(this, "Failed to show notification", Toast.LENGTH_SHORT).show();
        }
    }

    // ==================== EXPIRATION MANAGEMENT METHODS ====================

    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            CharSequence name = "License Expiration";
            String description = "Notifications for license expiration warnings";
            int importance = NotificationManager.IMPORTANCE_HIGH;
            NotificationChannel channel = new NotificationChannel(NOTIFICATION_CHANNEL_ID, name, importance);
            channel.setDescription(description);
            channel.enableLights(true);
            channel.setLightColor(Color.RED);
            channel.enableVibration(true);

            NotificationManager notificationManager = getSystemService(NotificationManager.class);
            notificationManager.createNotificationChannel(channel);
        }
    }



    @Override
    public void onActivityResult(int i, int i2, Intent intent) {
        super.onActivityResult(i, i2, intent);
        if (i != ACTION_MANAGE_OVERLAY_PERMISSION_REQUEST_CODE || Build.VERSION.SDK_INT < 23 || android.provider.Settings.canDrawOverlays(getApplicationContext())) {
            return;
        }
        RequestPermission();
    }

    @Override // android.app.Activity
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.menu_main, menu);
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem menuItem) {
        int itemId = menuItem.getItemId();
        if (itemId == R.id.inter) {
            startActivity(new Intent(this, (Class<?>) intsetting.class));
            return true;
        }
        if (itemId == R.id.forward) {
            startActivity(new Intent(this, com.appystore.mrecharge.activity.Settings.class));
            return true;
        }
        return super.onOptionsItemSelected(menuItem);
    }

    String randomCode() {
        UUID randomUUID = UUID.randomUUID();
        long leastSignificantBits = randomUUID.getLeastSignificantBits();
        long mostSignificantBits = randomUUID.getMostSignificantBits();

        long combined = Math.abs(mostSignificantBits ^ (mostSignificantBits >> 33))
                + Math.abs(leastSignificantBits ^ (leastSignificantBits >> 33));

        String code = String.format("%010d", combined);
        return code.substring(code.length() - 10);
    }

    private void RequestPermission() {
        if (Build.VERSION.SDK_INT >= 23) {
            startActivityForResult(new Intent("android.settings.action.MANAGE_OVERLAY_PERMISSION", Uri.parse("package:" + getPackageName())), ACTION_MANAGE_OVERLAY_PERMISSION_REQUEST_CODE);
        }
    }

    public void SavePreferences(String str, String str2) {
        SharedPreferences.Editor edit = PreferenceManager.getDefaultSharedPreferences(getApplicationContext()).edit();
        edit.putString(str, str2);
        edit.commit();
    }

    private boolean isMyServiceRunning(Class<?> cls) {
        Iterator<ActivityManager.RunningServiceInfo> it = ((ActivityManager) getSystemService("activity")).getRunningServices(Integer.MAX_VALUE).iterator();
        while (it.hasNext()) {
            if (cls.getName().equals(it.next().service.getClassName())) {
                return true;
            }
        }
        return false;
    }

    public static boolean hasPermissions(Context context, String... strArr) {
        if (context == null || strArr == null) {
            return true;
        }
        for (String str : strArr) {
            if (ActivityCompat.checkSelfPermission(context, str) != 0) {
                return false;
            }
        }
        return true;
    }

    public void startService() {
        Intent intent = new Intent(this, (Class<?>) sever.class);
        intent.putExtra("inputExtra", "Flexiload is running..");
        intent.putExtra("serv", "on");
        ContextCompat.startForegroundService(this, intent);
    }

    public void stopService() {
        if (isMyServiceRunning(sever.class)) {
            Intent intent = new Intent(this, (Class<?>) sever.class);
            intent.putExtra("inputExtra", "Flexiload is running..");
            intent.putExtra("serv", "off");
            ContextCompat.startForegroundService(this, intent);
        }
    }

    public static boolean isAccessibilityEnabled(Context context, String str) {
        Iterator<AccessibilityServiceInfo> it = ((AccessibilityManager) context.getSystemService("accessibility")).getEnabledAccessibilityServiceList(-1).iterator();
        while (it.hasNext()) {
            if (str.equals(it.next().getId())) {
                return true;
            }
        }
        return false;
    }

    public static String getPrefid(String str, Context context) {
        return PreferenceManager.getDefaultSharedPreferences(context).getString(str, null);
    }

    public static String getPref(String str, Context context) {
        return PreferenceManager.getDefaultSharedPreferences(context).getString(str, "");
    }

    public static String getPrefx(String str, Context context) {
        return PreferenceManager.getDefaultSharedPreferences(context).getString(str, "1234");
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void check(int i) {
        this.pDialog = new ProgressDialog(this);
        if (i == 0) {
            this.pDialog.setMessage("Checking...");
        } else {
            this.pDialog.setMessage("Registering Device...");
        }
        this.pDialog.setIndeterminate(false);
        this.pDialog.setCancelable(false);
        this.pDialog.show();

        // Get the API URL from shared preferences or use default
        String apiUrl = getApiUrl();

        // Log the API URL being used
        Log.d("API_CONFIG", "Using API endpoint: " + apiUrl);

        // Validate URL format
        try {
            URL url = new URL(apiUrl);
            // URL is valid, proceed
            Log.e("API_REQUEST", "Sending request to: " + apiUrl);
        } catch (MalformedURLException e) {
            Log.e("API_ERROR", "Invalid URL format: " + e.getMessage());
            this.pDialog.dismiss();
            Toast.makeText(getBaseContext(), "Invalid server URL format", Toast.LENGTH_LONG).show();
            this.buttonClick.setChecked(false);
            return;
        }

        // Try a different approach with a direct HTTP connection
        new Thread(new Runnable() {
            @Override
            public void run() {
                HttpURLConnection connection = null;
                try {
                    // Create connection
                    URL url = new URL(apiUrl);
                    connection = (HttpURLConnection) url.openConnection();
                    connection.setRequestMethod("POST");
                    connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
                    connection.setRequestProperty("User-Agent", "AppyStoreMRecharge Android App");
                    connection.setRequestProperty("Accept", "*/*");

                    // Enable input/output
                    connection.setDoInput(true);
                    connection.setDoOutput(true);

                    // Create parameters
                    String licenseKey = MainActivity.this.url.getText().toString().trim();
                    String pin = MainActivity.this.mpin.getText().toString().trim();
                    String deviceId = getPrefid("myid", getApplicationContext());
                    String deviceInfo = device_name();

                    String postData = "license_key=" + URLEncoder.encode(licenseKey, "UTF-8") +
                                     "&pin=" + URLEncoder.encode(pin, "UTF-8") +
                                     "&type=server" +
                                     "&pkg=platinum" +
                                     "&version=20" +
                                     "&device_id=" + URLEncoder.encode(deviceId, "UTF-8") +
                                     "&device_info=" + URLEncoder.encode(deviceInfo, "UTF-8") +
                                     "&timestamp=" + System.currentTimeMillis();

                    Log.d("API_PARAMS", "Sending params: " + postData);

                    // Send post data
                    try (DataOutputStream wr = new DataOutputStream(connection.getOutputStream())) {
                        wr.writeBytes(postData);
                        wr.flush();
                    }

                    // Get response
                    int responseCode = connection.getResponseCode();
                    Log.d("API_RESPONSE", "Response code: " + responseCode);

                    if (responseCode == HttpURLConnection.HTTP_OK) {
                        // Read response
                        StringBuilder response = new StringBuilder();
                        try (BufferedReader in = new BufferedReader(
                                new InputStreamReader(connection.getInputStream()))) {
                            String line;
                            while ((line = in.readLine()) != null) {
                                response.append(line);
                            }
                        }

                        final String responseData = response.toString();
                        Log.d("API_RESPONSE", "Response: " + responseData);

                        // Update UI on main thread
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                MainActivity.this.pDialog.dismiss();
                                MainActivity.this.FinalJSonObject = responseData;
                                new Checkstatus(getApplicationContext()).execute();
                            }
                        });
                    } else {
                        // Handle error
                        StringBuilder errorResponse = new StringBuilder();
                        try (BufferedReader in = new BufferedReader(
                                new InputStreamReader(connection.getErrorStream()))) {
                            String line;
                            while ((line = in.readLine()) != null) {
                                errorResponse.append(line);
                            }
                        }

                        final String errorData = errorResponse.toString();
                        Log.e("API_ERROR", "Error response: " + errorData);
                        Log.e("API_ERROR", "Response code: " + responseCode);

                        // Log headers
                        Map<String, List<String>> headers = connection.getHeaderFields();
                        for (Map.Entry<String, List<String>> entry : headers.entrySet()) {
                            Log.e("API_ERROR", "Header: " + entry.getKey() + " = " + entry.getValue());
                        }

                        // Update UI on main thread
                        final int finalResponseCode = responseCode;
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                MainActivity.this.pDialog.dismiss();
                                Toast.makeText(
                                        MainActivity.this.getBaseContext(),
                                        "Error code: " + finalResponseCode,
                                        Toast.LENGTH_LONG
                                ).show();
                                MainActivity.this.buttonClick.setChecked(false);
                            }
                        });
                    }
                } catch (final Exception e) {
                    Log.e("API_ERROR", "Exception: " + e.getMessage(), e);

                    // Update UI on main thread
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            MainActivity.this.pDialog.dismiss();
                            Toast.makeText(
                                    MainActivity.this.getBaseContext(),
                                    "Error: " + e.getMessage(),
                                    Toast.LENGTH_LONG
                            ).show();
                            MainActivity.this.buttonClick.setChecked(false);
                        }
                    });
                } finally {
                    if (connection != null) {
                        connection.disconnect();
                    }
                }
            }
        }).start();

        // No additional configuration needed - the HTTP request is handled in the thread
    }

    private class Checkstatus extends AsyncTask<Void, Void, Boolean> {
        private Context context;
        private String errorMessage = "";
        private JSONObject responseData = null;

        public Checkstatus(Context context) {
            this.context = context;
        }

        @Override
        protected void onPreExecute() {
            super.onPreExecute();
            Log.d("API_PROCESS", "Starting to process API response");
        }

        @Override
        protected Boolean doInBackground(Void... params) {
            try {
                // Check if we have a response
                if (MainActivity.this.FinalJSonObject == null) {
                    errorMessage = "No response received";
                    return false;
                }

                try {
                    // Parse the JSON response
                    Log.d("API_PROCESS", "Parsing JSON response: " + MainActivity.this.FinalJSonObject);
                    JSONArray jsonArray = new JSONArray(MainActivity.this.FinalJSonObject);

                    // Process the first object in the array
                    if (jsonArray.length() > 0) {
                        responseData = jsonArray.getJSONObject(0);

                        // Check status and version
                        int status = responseData.getInt("status");
                        int version = responseData.getInt("version");

                        Log.d("API_PROCESS", "Response status: " + status + ", version: " + version);

                        if (status == 1 && version == 1) {
                            // Valid response with correct version - approved
                            return true;
                        } else if (status == 2 && version == 1) {
                            // Status 2 means pending approval
                            if (responseData.has("message")) {
                                errorMessage = responseData.getString("message");
                            } else {
                                errorMessage = "Your device is pending approval by the administrator. Please try again later.";
                            }

                            // Save log ID if available for future reference
                            if (responseData.has("log_id")) {
                                String logId = responseData.getString("log_id");
                                MainActivity.this.SavePreferences("pending_approval_log_id", logId);
                            }

                            return false;
                        } else {
                            // Invalid status or version
                            if (version == 0) {
                                errorMessage = "Please update app!";
                            } else {
                                // Get the error message from the response if available
                                if (responseData.has("message")) {
                                    errorMessage = responseData.getString("message");
                                } else {
                                    errorMessage = "License validation failed";
                                }
                            }
                            return false;
                        }
                    } else {
                        errorMessage = "Empty response from server";
                        return false;
                    }
                } catch (JSONException e) {
                    Log.e("API_PROCESS", "JSON parsing error", e);
                    errorMessage = "Invalid response format";
                    return false;
                }
            } catch (Exception e) {
                Log.e("API_PROCESS", "Error processing response", e);
                errorMessage = "Error processing response: " + e.getMessage();
                return false;
            }
        }

        @Override
        protected void onPostExecute(Boolean success) {
            super.onPostExecute(success);

            if (success && responseData != null) {
                try {
                    // Save protocol (http/https)
                    if (responseData.getInt("sec") == 1) {
                        MainActivity.this.SavePreferences("sec", "https");
                    } else {
                        MainActivity.this.SavePreferences("sec", "http");
                    }

                    // Save credentials and settings
                    MainActivity.this.SavePreferences("pin", MainActivity.this.mpin.getText().toString());
                    MainActivity.this.SavePreferences(ImagesContract.URL, responseData.getString("domain"));

                    // Get the app license key (entered by user)
                    String appLicenseKey = MainActivity.this.url.getText().toString();
                    MainActivity.this.SavePreferences("licence", appLicenseKey);

                    // Check if server license key is in the response
                    if (responseData.has("server_license_key")) {
                        String serverLicenseKey = responseData.getString("server_license_key");

                        // Save the server license key
                        MainActivity.this.SavePreferences("server_license_key", serverLicenseKey);

                        // Check if the app license key matches the server license key
                        if (!appLicenseKey.equals(serverLicenseKey)) {
                            // Log the mismatch
                            Log.w("LICENSE_MISMATCH", "App license key (" + appLicenseKey +
                                  ") doesn't match server license key (" + serverLicenseKey + ")");

                            // Show license key mismatch warning
                            MainActivity.this.showLicenseKeyMismatchWarning(appLicenseKey, serverLicenseKey);
                        }
                    }

                    // Save additional data if available
                    if (responseData.has(ImagesContract.URL)) {
                        MainActivity.this.SavePreferences("curl", responseData.getString(ImagesContract.URL));
                    }

                    // Set service to active
                    SharedPreferences.Editor edit = MainActivity.this.getSharedPreferences("serv", 0).edit();
                    edit.putInt("stop", 1);
                    edit.apply();

                    // Get domain from response
                    String domain = responseData.getString("domain");

                    // Log domain information
                    Log.d("API_PROCESS", "Domain: " + domain);

                    // Note: Firebase messaging subscription removed to prevent crashes

                    // Generate a unique secret for this session
                    MainActivity.this.SavePreferences("secret", UUID.randomUUID().toString());

                    // Set flag for successful activation
                    MainActivity.this.al = true;

                    // Update UI and start service
                    MainActivity.this.getSharedPreferences("pref", 0).edit().putBoolean("server_on", true).apply();

                    // Check block status before starting service
                    MainActivity.this.checkBlockStatusBeforeStartingService();

                    // Show success message
                    Toast.makeText(
                            MainActivity.this.getBaseContext(),
                            "App active & running",
                            Toast.LENGTH_LONG
                    ).show();

                    // Register device with server
                    MainActivity.this.Device_reg();

                    // Log success
                    Log.d("API_PROCESS", "Successfully activated app");

                } catch (JSONException e) {
                    Log.e("API_PROCESS", "Error processing successful response", e);
                    MainActivity.this.buttonClick.setChecked(false);
                    Toast.makeText(
                            MainActivity.this.getBaseContext(),
                            "Error processing response",
                            Toast.LENGTH_LONG
                    ).show();
                }
            } else {
                // Handle failure
                MainActivity.this.buttonClick.setChecked(false);
                Toast.makeText(
                        MainActivity.this.getBaseContext(),
                        errorMessage,
                        Toast.LENGTH_LONG
                ).show();
                MainActivity.this.flag = true;

                Log.e("API_PROCESS", "Activation failed: " + errorMessage + " | License key: " + MainActivity.this.url.getText().toString() + " | PIN: " + MainActivity.this.mpin.getText().toString());
            }
        }
    }
    public void Device_reg() {
        // Create server URL from preferences
        String baseUrl = getPref("sec", getApplicationContext()) + "://" + getPref(ImagesContract.URL, getApplicationContext());

        // Log the base URL for debugging
        Log.d("DeviceReg", "Base URL: " + baseUrl);

        // Get user information from preferences
        String m1 = getPref("m1", getApplicationContext());
        String m2 = getPref("m2", getApplicationContext());
        String m3 = getPref("m3", getApplicationContext());
        String m4 = getPref("m4", getApplicationContext());
        String m5 = getPref("m5", getApplicationContext());
        String m6 = getPref("m6", getApplicationContext());
        String m7 = getPref("m7", getApplicationContext());
        String m8 = getPref("m8", getApplicationContext());
        String myId = getPrefid("myid", getApplicationContext());
        String deviceName = device_name();

        // Log the device ID and name for debugging
        Log.d("DeviceReg", "Device ID: " + myId);
        Log.d("DeviceReg", "Device Name: " + deviceName);
        Log.d("DeviceReg", "M1-M8 values: " + m1 + ", " + m2 + ", " + m3 + ", " + m4 + ", " + m5 + ", " + m6 + ", " + m7 + ", " + m8);

        try {
            // Create Retrofit client
            Devicer apiService = NetworkClient
                    .getRetrofitClient(baseUrl)
                    .create(Devicer.class);

            // Make API call with callback
            apiService.Devicer(m1, m2, m3, m4, m5, m6, m7, m8, myId, deviceName)
                    .enqueue(new Callback<WResponse>() {
                        @Override
                        public void onResponse(Call<WResponse> call, retrofit2.Response<WResponse> response) {
                            if (response.isSuccessful()) {
                                // Successfully received response
                                WResponse wResponse = response.body();
                                if (wResponse != null) {
                                    Integer status = wResponse.getstatus();
                                    if (status != null && status == 1) {
                                        // Registration successful
                                        Log.d("DeviceReg", "Registration successful with status: " + status);
                                        Toast.makeText(getApplicationContext(), "Device registered successfully", Toast.LENGTH_SHORT).show();
                                    } else {
                                        // Registration failed with error status
                                        Log.e("DeviceReg", "Registration failed with status: " + (status != null ? status : "null"));
                                        Toast.makeText(getApplicationContext(), "Device registration failed", Toast.LENGTH_SHORT).show();
                                    }
                                } else {
                                    // Empty response body
                                    Log.e("DeviceReg", "Empty response body");
                                    Toast.makeText(getApplicationContext(), "Empty response from server", Toast.LENGTH_SHORT).show();
                                }
                            } else {
                                // HTTP error
                                Log.e("DeviceReg", "HTTP error: " + response.code() + " - " + response.message());
                                try {
                                    String errorBody = response.errorBody() != null ? response.errorBody().string() : "No error body";
                                    Log.e("DeviceReg", "Error body: " + errorBody);
                                } catch (Exception e) {
                                    Log.e("DeviceReg", "Error reading error body: " + e.getMessage());
                                }
                                Toast.makeText(getApplicationContext(), "Server error: " + response.code(), Toast.LENGTH_SHORT).show();

                                // Try alternative method if HTTP error
                                tryAlternativeRegistration(baseUrl, m1, m2, m3, m4, m5, m6, m7, m8, myId, deviceName);
                            }
                        }

                        @Override
                        public void onFailure(Call<WResponse> call, Throwable t) {
                            // Network or other error
                            Log.e("DeviceReg", "Registration failed: " + t.getMessage(), t);
                            Toast.makeText(getApplicationContext(), "Connection error: " + t.getMessage(), Toast.LENGTH_SHORT).show();

                            // Try alternative method if Retrofit fails
                            tryAlternativeRegistration(baseUrl, m1, m2, m3, m4, m5, m6, m7, m8, myId, deviceName);
                        }
                    });
        } catch (Exception e) {
            // Handle any exceptions during setup
            Log.e("DeviceReg", "Exception setting up API call: " + e.getMessage(), e);
            Toast.makeText(getApplicationContext(), "Error: " + e.getMessage(), Toast.LENGTH_SHORT).show();

            // Try alternative method if Retrofit setup fails
            tryAlternativeRegistration(baseUrl, m1, m2, m3, m4, m5, m6, m7, m8, myId, deviceName);
        }
    }
    /**
     * Try an alternative method to register the device using HttpURLConnection
     */
    private void tryAlternativeRegistration(final String baseUrl, final String m1, final String m2,
                                            final String m3, final String m4, final String m5,
                                            final String m6, final String m7, final String m8,
                                            final String deviceId, final String deviceName) {
        new Thread(new Runnable() {
            @Override
            public void run() {
                HttpURLConnection connection = null;
                try {
                    // Create URL for the device endpoint
                    URL url = new URL(baseUrl + "/Modemcon/device");
                    Log.d("DeviceReg", "Alternative method URL: " + url.toString());

                    // Open connection
                    connection = (HttpURLConnection) url.openConnection();
                    connection.setRequestMethod("POST");
                    connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
                    connection.setDoOutput(true);

                    // Create POST data
                    String postData = "m1=" + URLEncoder.encode(m1, "UTF-8") +
                            "&m2=" + URLEncoder.encode(m2, "UTF-8") +
                            "&m3=" + URLEncoder.encode(m3, "UTF-8") +
                            "&m4=" + URLEncoder.encode(m4, "UTF-8") +
                            "&m5=" + URLEncoder.encode(m5, "UTF-8") +
                            "&m6=" + URLEncoder.encode(m6, "UTF-8") +
                            "&m7=" + URLEncoder.encode(m7, "UTF-8") +
                            "&m8=" + URLEncoder.encode(m8, "UTF-8") +
                            "&device_id=" + URLEncoder.encode(deviceId, "UTF-8") +
                            "&name=" + URLEncoder.encode(deviceName, "UTF-8");

                    Log.d("DeviceReg", "Alternative method POST data: " + postData);

                    // Send POST data
                    try (DataOutputStream wr = new DataOutputStream(connection.getOutputStream())) {
                        wr.writeBytes(postData);
                        wr.flush();
                    }

                    // Get response
                    int responseCode = connection.getResponseCode();
                    Log.d("DeviceReg", "Alternative method response code: " + responseCode);

                    if (responseCode == HttpURLConnection.HTTP_OK) {
                        // Read response
                        StringBuilder response = new StringBuilder();
                        try (BufferedReader in = new BufferedReader(
                                new InputStreamReader(connection.getInputStream()))) {
                            String line;
                            while ((line = in.readLine()) != null) {
                                response.append(line);
                            }
                        }

                        final String responseData = response.toString();
                        Log.d("DeviceReg", "Alternative method response: " + responseData);

                        // Update UI on main thread
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                Toast.makeText(getApplicationContext(),
                                        "Device registered successfully (alternative method)",
                                        Toast.LENGTH_SHORT).show();
                            }
                        });
                    } else {
                        // Handle error
                        StringBuilder errorResponse = new StringBuilder();
                        try (BufferedReader in = new BufferedReader(
                                new InputStreamReader(connection.getErrorStream()))) {
                            String line;
                            while ((line = in.readLine()) != null) {
                                errorResponse.append(line);
                            }
                        }

                        final String errorData = errorResponse.toString();
                        Log.e("DeviceReg", "Alternative method error: " + errorData);

                        // Update UI on main thread
                        final int finalResponseCode = responseCode;
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                Toast.makeText(getApplicationContext(),
                                        "Registration failed (alternative method): " + finalResponseCode,
                                        Toast.LENGTH_SHORT).show();
                            }
                        });
                    }
                } catch (final Exception e) {
                    Log.e("DeviceReg", "Alternative method exception: " + e.getMessage(), e);

                    // Update UI on main thread
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            Toast.makeText(getApplicationContext(),
                                    "Registration error: " + e.getMessage(),
                                    Toast.LENGTH_SHORT).show();
                        }
                    });
                } finally {
                    if (connection != null) {
                        connection.disconnect();
                    }
                }
            }
        }).start();
    }

    /* JADX INFO: Access modifiers changed from: private */
//    public void Device_reg() {
//        // সার্ভার URL তৈরি করা (sec ও URL প্রেফারেন্স থেকে)
//        String baseUrl = getPref("sec", getApplicationContext()) + "://" + getPref(ImagesContract.URL, getApplicationContext());
//
//        // ইউজারের ইনফরমেশনগুলো প্রেফারেন্স থেকে নেওয়া
//        String m1 = getPref("m1", getApplicationContext());
//        String m2 = getPref("m2", getApplicationContext());
//        String m3 = getPref("m3", getApplicationContext());
//        String m4 = getPref("m4", getApplicationContext());
//        String m5 = getPref("m5", getApplicationContext());
//        String m6 = getPref("m6", getApplicationContext());
//        String m7 = getPref("m7", getApplicationContext());
//        String m8 = getPref("m8", getApplicationContext());
//        String myId = getPrefid("myid", getApplicationContext());
//        String deviceName = device_name();
//
//        // Retrofit ক্লায়েন্ট তৈরি
//        Devicer apiService = NetworkClient
//                .getRetrofitClient(baseUrl)
//                .create(Devicer.class);
//
//        // API কল এবং Callback সেট করা
//        apiService.Devicer(m1, m2, m3, m4, m5, m6, m7, m8, myId, deviceName)
//                .enqueue(new Callback() {
//                    @Override
//                    public void onResponse(Call call, retrofit2.Response response) {
//                        // সার্ভার থেকে সফল রেসপন্স পেলে এখানে কোড লিখতে পারেন
//                        Log.d("DeviceReg", "Registration success: " + response.message());
//                    }
//
//                    @Override
//                    public void onFailure(Call call, Throwable t) {
//                        // কোনো সমস্যা হলে এখানে হ্যান্ডেল করতে পারেন
//                        Log.e("DeviceReg", "Registration failed: " + t.getMessage());
//                    }
//                });
//    }


    private boolean isAccessibilitySettingsOn(Context context) {
        int i;
        String string;
        String str = getPackageName() + "/" + USSDService.class.getCanonicalName();
        try {
            i = Settings.Secure.getInt(context.getApplicationContext().getContentResolver(), "accessibility_enabled");
        } catch (Settings.SettingNotFoundException unused) {
            i = 0;
        }
        TextUtils.SimpleStringSplitter simpleStringSplitter = new TextUtils.SimpleStringSplitter(':');
        if (i == 1 && (string = Settings.Secure.getString(context.getApplicationContext().getContentResolver(), "enabled_accessibility_services")) != null) {
            simpleStringSplitter.setString(string);
            while (simpleStringSplitter.hasNext()) {
                if (simpleStringSplitter.next().equalsIgnoreCase(str)) {
                    return true;
                }
            }
        }
        return false;
    }

    /* JADX INFO: Access modifiers changed from: private */
    public void askIgnoreOptimization() {
        if (Build.VERSION.SDK_INT >= 23) {
            Intent intent = new Intent("android.settings.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS");
            intent.setData(Uri.parse("package:" + getPackageName()));
            startActivityForResult(intent, 1002);
        }
    }

    public static boolean hasper(Context context, String... strArr) {
        if (context == null || strArr == null) {
            return true;
        }
        for (String str : strArr) {
            if (ActivityCompat.checkSelfPermission(context, str) != 0) {
                return false;
            }
        }
        return true;
    }

    @Override // androidx.fragment.app.FragmentActivity, android.app.Activity
    protected void onResume() {
        super.onResume();
        // Check if the app is already activated
        if (this.al) {
            // App is already activated, start the service
            startService();
        } else {
            // Check if we have a pending approval
            String pendingLogId = getPref("pending_approval_log_id", getApplicationContext());
            if (pendingLogId != null && !pendingLogId.isEmpty()) {
                // We have a pending approval, check its status
                checkApprovalStatus();
            }
        }
    }

    /**
     * Check if a pending approval has been approved
     */
    private void checkApprovalStatus() {
        String pendingLogId = getPref("pending_approval_log_id", getApplicationContext());
        if (pendingLogId == null || pendingLogId.isEmpty()) {
            return; // No pending approval
        }

        // Show progress dialog
        final ProgressDialog progressDialog = new ProgressDialog(this);
        progressDialog.setMessage("Checking approval status...");
        progressDialog.setIndeterminate(true);
        progressDialog.setCancelable(false);
        progressDialog.show();

        // Get the API URL
        final String apiUrl = getApiUrl();

        // Create a new thread for the network request
        new Thread(new Runnable() {
            @Override
            public void run() {
                HttpURLConnection connection = null;
                try {
                    // Create URL
                    URL url = new URL(apiUrl);

                    // Open connection
                    connection = (HttpURLConnection) url.openConnection();
                    connection.setRequestMethod("POST");
                    connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
                    connection.setDoOutput(true);

                    // Prepare POST data
                    String postData = "check_approval=1" +
                            "&log_id=" + pendingLogId +
                            "&license_key=" + MainActivity.this.url.getText().toString() +
                            "&device_id=" + Settings.Secure.getString(MainActivity.this.getContentResolver(), "android_id");

                    // Send POST data
                    try (OutputStream os = connection.getOutputStream()) {
                        os.write(postData.getBytes("UTF-8"));
                    }

                    // Get response
                    int responseCode = connection.getResponseCode();

                    if (responseCode == HttpURLConnection.HTTP_OK) {
                        // Read response
                        StringBuilder response = new StringBuilder();
                        try (BufferedReader in = new BufferedReader(
                                new InputStreamReader(connection.getInputStream()))) {
                            String line;
                            while ((line = in.readLine()) != null) {
                                response.append(line);
                            }
                        }

                        final String responseData = response.toString();
                        Log.d("APPROVAL_CHECK", "Response: " + responseData);

                        // Process response on UI thread
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                progressDialog.dismiss();

                                try {
                                    JSONArray jsonArray = new JSONArray(responseData);
                                    if (jsonArray.length() > 0) {
                                        JSONObject jsonObject = jsonArray.getJSONObject(0);
                                        int status = jsonObject.getInt("status");

                                        if (status == 1) {
                                            // Approval granted, retry activation
                                            Toast.makeText(MainActivity.this,
                                                "Your device has been approved! Activating...",
                                                Toast.LENGTH_LONG).show();

                                            // Clear pending approval ID
                                            SavePreferences("pending_approval_log_id", "");

                                            // Trigger activation
                                            check(0);
                                        } else if (status == 2) {
                                            // Still pending
                                            Toast.makeText(MainActivity.this,
                                                "Your device is still pending approval. Please try again later.",
                                                Toast.LENGTH_LONG).show();
                                        } else {
                                            // Rejected or other error
                                            String message = jsonObject.has("message") ?
                                                jsonObject.getString("message") :
                                                "Your approval request was not approved.";

                                            Toast.makeText(MainActivity.this, message, Toast.LENGTH_LONG).show();

                                            // Clear pending approval ID
                                            SavePreferences("pending_approval_log_id", "");
                                        }
                                    }
                                } catch (Exception e) {
                                    Log.e("APPROVAL_CHECK", "Error processing response", e);
                                    Toast.makeText(MainActivity.this,
                                        "Error checking approval status: " + e.getMessage(),
                                        Toast.LENGTH_LONG).show();
                                }
                            }
                        });
                    } else {
                        // Handle error
                        final int finalResponseCode = responseCode;
                        runOnUiThread(new Runnable() {
                            @Override
                            public void run() {
                                progressDialog.dismiss();
                                Toast.makeText(MainActivity.this,
                                    "Error checking approval status. Code: " + finalResponseCode,
                                    Toast.LENGTH_LONG).show();
                            }
                        });
                    }
                } catch (final Exception e) {
                    Log.e("APPROVAL_CHECK", "Exception: " + e.getMessage(), e);

                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            progressDialog.dismiss();
                            Toast.makeText(MainActivity.this,
                                "Error: " + e.getMessage(),
                                Toast.LENGTH_LONG).show();
                        }
                    });
                } finally {
                    if (connection != null) {
                        connection.disconnect();
                    }
                }
            }
        }).start();
    }

    @Override // androidx.fragment.app.FragmentActivity, android.app.Activity
    protected void onPause() {
        super.onPause();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        // Clean up resources if needed
    }

    private String device_name() {
        return Build.MANUFACTURER + " " + Build.MODEL + " V:" + Build.VERSION.RELEASE;
    }

    /**
     * Get the API URL from shared preferences or use the default URL
     * @return The API URL to use for authentication
     */
    private String getApiUrl() {
        // Default API URL
        String defaultApiUrl = "http://192.168.0.106/AppyStoreMRecharge/admin/api/device_auth.php";

        // Try to get the API URL from shared preferences
        String savedApiUrl = PreferenceManager.getDefaultSharedPreferences(getApplicationContext())
                .getString("api_url", null);

        // If no saved URL or it's empty, use the default and save it
        if (savedApiUrl == null || savedApiUrl.isEmpty()) {
            // Save the default URL for future use
            SavePreferences("api_url", defaultApiUrl);
            return defaultApiUrl;
        }

        return savedApiUrl;
    }

    /**
     * Set a new API URL to use for authentication
     * @param newApiUrl The new API URL to use
     */
    private void setApiUrl(String newApiUrl) {
        if (newApiUrl != null && !newApiUrl.isEmpty()) {
            // Validate URL format
            try {
                URL url = new URL(newApiUrl);
                // URL is valid, save it
                SavePreferences("api_url", newApiUrl);
                Log.d("API_CONFIG", "API URL updated to: " + newApiUrl);
            } catch (MalformedURLException e) {
                Log.e("API_ERROR", "Invalid API URL format: " + e.getMessage());
            }
        }
    }

    /**
     * Reset the button click counter
     */
    private void resetButtonClickCount() {
        buttonClickCount = 0;
        lastClickResetTime = System.currentTimeMillis();
        buttonCooldownActive = false;
    }

    /**
     * Increment the button click counter
     * Reset the counter if it's been more than 10 seconds since the last click
     */
    private void incrementButtonClickCount() {
        long currentTime = System.currentTimeMillis();

        // Reset counter if it's been more than 10 seconds since the last click
        if (currentTime - lastClickResetTime > 10000) {
            buttonClickCount = 0;
        }

        buttonClickCount++;
        lastClickResetTime = currentTime;

        Log.d("BUTTON_CLICKS", "Button click count: " + buttonClickCount);
    }

    /**
     * Show a warning alert after 3 clicks with activity validation
     */
    private void showWarningAlert() {
        if (!isActivityValid()) {
            Log.w(TAG, "Cannot show warning alert - activity is not valid");
            return;
        }

        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle("⚠️ Warning");
        builder.setMessage("You are clicking too frequently. One more click will disable the button for 5 minutes.");
        builder.setPositiveButton("OK", null);
        showDialogSafely(builder, "warning alert");
    }

    /**
     * Show a danger alert and disable the button for 5 minutes after 4 clicks with activity validation
     */
    private void showDangerAlertAndDisableButton() {
        if (!isActivityValid()) {
            Log.w(TAG, "Cannot show danger alert - activity is not valid");
            return;
        }

        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle("🚫 Button Disabled");
        builder.setMessage("Due to excessive clicking, this button has been disabled for 5 minutes.");
        builder.setPositiveButton("OK", null);
        showDialogSafely(builder, "danger alert");

        // Set cooldown flag
        buttonCooldownActive = true;

        // Schedule re-enabling the button after 5 minutes
        handler.postDelayed(new Runnable() {
            @Override
            public void run() {
                buttonCooldownActive = false;
                resetButtonClickCount();
                Toast.makeText(MainActivity.this, "Button has been re-enabled", Toast.LENGTH_SHORT).show();
            }
        }, BUTTON_COOLDOWN_TIME);
    }

    /**
     * Check if there's a mismatch between the saved app license key and server license key
     */
    private void checkLicenseKeyMismatch() {
        try {
            // Get the saved license keys
            String appLicenseKey = getPref("licence", getApplicationContext());
            String serverLicenseKey = getPref("server_license_key", getApplicationContext());

            // Check if both keys exist and are not null
            if (appLicenseKey != null && serverLicenseKey != null &&
                !appLicenseKey.isEmpty() && !serverLicenseKey.isEmpty() &&
                !appLicenseKey.equals(serverLicenseKey)) {

                Log.w("LICENSE_MISMATCH", "Saved app license key (" + appLicenseKey +
                      ") doesn't match saved server license key (" + serverLicenseKey + ")");

                // Show the mismatch warning
                showLicenseKeyMismatchWarning(appLicenseKey, serverLicenseKey);
            }
        } catch (Exception e) {
            // Log the error but don't crash the app
            Log.e("LICENSE_MISMATCH", "Error checking license key mismatch: " + e.getMessage(), e);
        }
    }

    /**
     * Show a warning dialog when the app license key doesn't match the server license key
     * @param appLicenseKey The license key entered by the user
     * @param serverLicenseKey The license key returned by the server
     */
    private void showLicenseKeyMismatchWarning(final String appLicenseKey, final String serverLicenseKey) {
        // Safety check to prevent null pointer exceptions
        if (appLicenseKey == null || serverLicenseKey == null) {
            Log.e("LICENSE_MISMATCH", "Cannot show warning dialog - license keys are null");
            return;
        }

        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                AlertDialog.Builder builder = null;
                try {
                    builder = new AlertDialog.Builder(MainActivity.this);
                    builder.setTitle("License Key Mismatch Warning");

                    // Create a message with both license keys
                    String message = "The license key you entered doesn't match the server license key.\n\n" +
                                    "Your license key: " + appLicenseKey + "\n" +
                                    "Server license key: " + serverLicenseKey + "\n\n" +
                                    "This may cause issues with app functionality. Would you like to update your license key?";

                    builder.setMessage(message);

                    // Add buttons
                    builder.setPositiveButton("Update License Key", new DialogInterface.OnClickListener() {
                        @Override
                        public void onClick(DialogInterface dialog, int which) {
                            // Update the license key field with the server license key
                            if (url != null) {
                                url.setText(serverLicenseKey);
                            }
                            SavePreferences("licence", serverLicenseKey);

                            Toast.makeText(getBaseContext(), "License key updated", Toast.LENGTH_SHORT).show();
                        }
                    });
                } catch (Exception e) {
                    Log.e("LICENSE_MISMATCH", "Error showing license key mismatch dialog: " + e.getMessage(), e);
                    return; // Exit if we couldn't create the dialog
                }

                // Continue only if builder was successfully created
                if (builder != null) {
                    try {
                        builder.setNegativeButton("Keep My Key", new DialogInterface.OnClickListener() {
                            @Override
                            public void onClick(DialogInterface dialog, int which) {
                                // User wants to keep their key, just show a toast
                                Toast.makeText(getBaseContext(),
                                    "Using your license key. Some features may not work correctly.",
                                    Toast.LENGTH_LONG).show();
                            }
                        });

                        // Show the dialog
                        AlertDialog dialog = builder.create();
                        dialog.show();
                    } catch (Exception e) {
                        Log.e("LICENSE_MISMATCH", "Error showing dialog: " + e.getMessage(), e);
                    }
                }
            }
        });
    }

    /**
     * Test if the API endpoint is reachable with a real POST request
     * This helps diagnose connection issues with the actual API endpoint
     */
    private void testServerConnection() {
        // Show a progress dialog
        final ProgressDialog testDialog = new ProgressDialog(this);
        testDialog.setMessage("Testing server connection...");
        testDialog.setIndeterminate(true);
        testDialog.setCancelable(false);
        testDialog.show();

        new Thread(new Runnable() {
            @Override
            public void run() {
                HttpURLConnection connection = null;
                try {
                    // Create a real POST request to the API endpoint
                    URL url = new URL(getApiUrl());
                    connection = (HttpURLConnection) url.openConnection();
                    connection.setRequestMethod("POST");
                    connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
                    connection.setRequestProperty("User-Agent", "AppyStoreMRecharge Android App");
                    connection.setRequestProperty("Accept", "*/*");
                    connection.setConnectTimeout(10000);
                    connection.setReadTimeout(10000);

                    // Enable input/output
                    connection.setDoInput(true);
                    connection.setDoOutput(true);

                    // Create test parameters
                    String postData = "license_key=test-license" +
                                     "&pin=1234" +
                                     "&type=server" +
                                     "&pkg=platinum" +
                                     "&version=20" +
                                     "&device_id=test-device" +
                                     "&device_info=Test Device" +
                                     "&timestamp=" + System.currentTimeMillis();

                    Log.d("SERVER_TEST", "Sending test params: " + postData);

                    // Send post data
                    try (DataOutputStream wr = new DataOutputStream(connection.getOutputStream())) {
                        wr.writeBytes(postData);
                        wr.flush();
                    }

                    // Get response
                    int responseCode = connection.getResponseCode();
                    Log.d("SERVER_TEST", "Response code: " + responseCode);

                    // Log headers
                    Map<String, List<String>> headers = connection.getHeaderFields();
                    for (Map.Entry<String, List<String>> entry : headers.entrySet()) {
                        if (entry.getKey() != null) {
                            Log.d("SERVER_TEST", "Header: " + entry.getKey() + " = " + entry.getValue());
                        }
                    }

                    // Read response data
                    StringBuilder responseData = new StringBuilder();
                    try (BufferedReader reader = new BufferedReader(
                            new InputStreamReader(
                                    responseCode >= 400 ? connection.getErrorStream() : connection.getInputStream()))) {
                        String line;
                        while ((line = reader.readLine()) != null) {
                            responseData.append(line);
                        }
                    }

                    Log.d("SERVER_TEST", "Response data: " + responseData.toString());

                    final String result;
                    if (responseCode == HttpURLConnection.HTTP_OK) {
                        result = "API test: SUCCESS";
                    } else {
                        result = "API test: FAILED (Code: " + responseCode + ")";
                    }

                    final String responseContent = responseData.toString();

                    // Update UI on main thread
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            testDialog.dismiss();

                            // Create and show a detailed dialog with the test results
                            AlertDialog.Builder builder = new AlertDialog.Builder(MainActivity.this);
                            builder.setTitle("API Connection Test");

                            String message = result + "\n\n";
                            if (!responseContent.isEmpty()) {
                                message += "Response: " + responseContent;
                            }

                            builder.setMessage(message);
                            builder.setPositiveButton("OK", null);
                            builder.show();
                        }
                    });

                } catch (final Exception e) {
                    Log.e("SERVER_TEST", "Exception: " + e.getMessage(), e);

                    // Update UI on main thread
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            testDialog.dismiss();

                            // Create and show a detailed dialog with the error
                            AlertDialog.Builder builder = new AlertDialog.Builder(MainActivity.this);
                            builder.setTitle("API Connection Test Failed");
                            builder.setMessage("Error: " + e.getMessage());
                            builder.setPositiveButton("OK", null);
                            builder.show();
                        }
                    });
                } finally {
                    if (connection != null) {
                        connection.disconnect();
                    }
                }
            }
        }).start();
    }
}



